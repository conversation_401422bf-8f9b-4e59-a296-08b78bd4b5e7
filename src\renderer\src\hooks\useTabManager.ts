import { useMemo, useState, useCallback } from 'react'
import type { Tab } from '@/types'

export function useTabManager(tabs: Tab[], maxVisibleTabs: number) {
  const [scrollOffset, setScrollOffset] = useState(0)

  const activeTabIndex = useMemo(() => tabs.findIndex((tab) => tab.active), [tabs])

  const visibleRange = useMemo(() => {
    if (tabs.length <= maxVisibleTabs) {
      return { start: 0, end: tabs.length }
    }

    let start = scrollOffset
    let end = start + maxVisibleTabs

    // 确保活跃标签页可见
    if (activeTabIndex >= 0) {
      if (activeTabIndex < start) {
        start = activeTabIndex
        end = start + maxVisibleTabs
      } else if (activeTabIndex >= end) {
        end = activeTabIndex + 1
        start = end - maxVisibleTabs
      }
    }

    // 边界检查
    start = Math.max(0, start)
    end = Math.min(tabs.length, end)

    if (end - start < maxVisibleTabs) {
      start = Math.max(0, end - maxVisibleTabs)
    }

    return { start, end }
  }, [tabs.length, maxVisibleTabs, scrollOffset, activeTabIndex])

  const canScrollLeft = visibleRange.start > 0
  const canScrollRight = visibleRange.end < tabs.length

  const scrollLeft = useCallback(() => {
    setScrollOffset(Math.max(0, scrollOffset - 1))
  }, [scrollOffset])

  const scrollRight = useCallback(() => {
    setScrollOffset(Math.min(tabs.length - maxVisibleTabs, scrollOffset + 1))
  }, [scrollOffset, tabs.length, maxVisibleTabs])

  return {
    visibleRange,
    activeTabIndex,
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight
  }
}
