# 窗口配置说明

## 单例模式

应用程序采用单例模式，确保同时只能运行一个实例：

- ✅ **防止多实例运行** - 当尝试启动第二个实例时，会自动退出
- ✅ **智能窗口聚焦** - 第二个实例启动时会聚焦到已存在的窗口
- ✅ **状态恢复** - 如果窗口被最小化或隐藏，会自动恢复并显示

### 实现细节

```typescript
// 请求单例锁
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  // 如果获取锁失败，退出当前实例
  app.quit()
} else {
  // 监听第二个实例启动事件
  app.on('second-instance', () => {
    // 聚焦现有窗口
    focusExistingWindow()
  })
}
```

## 窗口大小限制

### 最小尺寸限制

- **最小宽度**: 1000px
- **最小高度**: 700px

### 默认尺寸

- **默认宽度**: 1400px
- **默认高度**: 900px

### 特性

- ✅ **可调整大小** - 用户可以自由调整窗口大小
- ✅ **最小限制强制** - 窗口不能小于最小尺寸
- ✅ **状态保存** - 窗口大小和位置会自动保存
- ✅ **启动恢复** - 下次启动时恢复上次的窗口状态

### 实现机制

```typescript
// 窗口配置
const MIN_WIDTH = 1000
const MIN_HEIGHT = 700
const DEFAULT_WIDTH = 1400
const DEFAULT_HEIGHT = 900

// 创建窗口时设置限制
const mainWindow = new BrowserWindow({
  minWidth: MIN_WIDTH,
  minHeight: MIN_HEIGHT,
  resizable: true,
  // ...其他配置
})

// 监听窗口大小变化，确保不小于最小限制
mainWindow.on('resize', () => {
  const bounds = mainWindow.getBounds()
  if (bounds.width < MIN_WIDTH || bounds.height < MIN_HEIGHT) {
    mainWindow.setBounds({
      ...bounds,
      width: Math.max(bounds.width, MIN_WIDTH),
      height: Math.max(bounds.height, MIN_HEIGHT)
    })
  }
})
```

## 状态持久化

### 保存的状态

- 窗口位置 (x, y)
- 窗口大小 (width, height)
- 最大化状态
- 聚焦状态

### 自动保存时机

- 窗口大小改变时
- 窗口位置改变时
- 窗口状态改变时（最大化/最小化）
- 应用退出时

### 恢复机制

启动时会自动恢复上次保存的窗口状态，包括：

1. **位置恢复** - 恢复到上次关闭时的位置
2. **大小恢复** - 恢复到上次的窗口大小（不小于最小限制）
3. **状态恢复** - 如果上次是最大化状态，会自动最大化

## 平台兼容性

### Windows
- ✅ 完全支持所有功能
- ✅ 任务栏显示应用名称
- ✅ 单例模式正常工作

### macOS
- ✅ 支持所有功能
- ✅ Dock 图标显示
- ✅ 原生窗口控制按钮隐藏
- ✅ 标题栏样式适配

### Linux
- ✅ 基本功能支持
- ✅ 窗口管理器兼容
- ✅ 图标显示

## 用户体验

### 优势

1. **一致性** - 确保用户界面的一致性体验
2. **性能** - 避免多实例造成的资源浪费
3. **数据安全** - 防止多实例同时访问数据库
4. **用户友好** - 智能的窗口聚焦机制

### 使用建议

- 建议在较大的显示器上使用，以充分利用应用功能
- 最小化时应用会保持在后台运行
- 可以通过设置界面调整状态保存选项
