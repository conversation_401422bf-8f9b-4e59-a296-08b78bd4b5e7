import { useEffect, useCallback } from 'react'
import { useSettings } from './useSettings'
import { useTitlebarStore } from '@/stores/titlebar-store'

export function useAppState() {
  const { settings, getSetting } = useSettings()
  const { tabs, saveTabs } = useTitlebarStore()

  // 保存应用状态
  const saveAppState = useCallback(async () => {
    if (!getSetting('saveAppState')) {
      return
    }

    try {
      // 保存标签页状态
      if (getSetting('restoreTabs')) {
        await saveTabs()
      }

      // 保存窗口状态
      if (getSetting('restoreWindowState')) {
        const windowState = await window.api.window.getState()
        if (windowState.success && windowState.data) {
          await window.api.database.saveWindowState(windowState.data)
        }
      }
    } catch (error) {
      console.error('Failed to save app state:', error)
    }
  }, [getSetting, saveTabs])

  // 恢复应用状态
  const restoreAppState = useCallback(async () => {
    if (!getSetting('saveAppState')) {
      return
    }

    try {
      // 恢复窗口状态
      if (getSetting('restoreWindowState')) {
        const response = await window.api.database.getWindowState()
        if (response.success && response.data) {
          // 这里可以通过IPC通知主进程恢复窗口状态
          // 主进程会在创建窗口时处理这个状态
        }
      }

      // 标签页状态在App.tsx中已经处理了
    } catch (error) {
      console.error('Failed to restore app state:', error)
    }
  }, [getSetting])

  // 监听应用退出事件，保存状态
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveAppState()
    }

    // 监听页面卸载事件
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 定期保存状态（每30秒）
    const saveInterval = setInterval(() => {
      if (getSetting('saveAppState')) {
        saveAppState()
      }
    }, 30000)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      clearInterval(saveInterval)
    }
  }, [saveAppState, getSetting])

  // 监听标签页变化，自动保存
  useEffect(() => {
    if (getSetting('saveAppState') && getSetting('restoreTabs')) {
      const timeoutId = setTimeout(() => {
        saveTabs()
      }, 1000) // 延迟1秒保存，避免频繁保存

      return () => clearTimeout(timeoutId)
    }
  }, [tabs, getSetting, saveTabs])

  return {
    saveAppState,
    restoreAppState,
    settings
  }
}
