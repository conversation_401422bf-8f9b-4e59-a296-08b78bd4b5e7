import { SettingsRepository } from '../repositories/settings-repository'

export class SettingsService {
  private settingsRepository: SettingsRepository

  constructor(settingsRepository: SettingsRepository) {
    this.settingsRepository = settingsRepository
  }

  /**
   * 保存设置
   */
  async saveSetting(key: string, value: any): Promise<void> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    // 验证设置值
    this.validateSettingValue(value)

    await this.settingsRepository.saveSetting(key, value)
  }

  /**
   * 获取设置
   */
  async getSetting<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    return await this.settingsRepository.getSetting(key, defaultValue)
  }

  /**
   * 获取所有设置
   */
  async getAllSettings(): Promise<Record<string, any>> {
    return await this.settingsRepository.getAllSettings()
  }

  /**
   * 删除设置
   */
  async deleteSetting(key: string): Promise<void> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    await this.settingsRepository.deleteSetting(key)
  }

  /**
   * 批量保存设置
   */
  async saveSettings(settings: Record<string, any>): Promise<void> {
    if (!settings || typeof settings !== 'object') {
      throw new Error('Settings must be an object')
    }

    // 验证所有设置值
    for (const [key, value] of Object.entries(settings)) {
      if (!key) {
        throw new Error('Setting key cannot be empty')
      }
      this.validateSettingValue(value)
    }

    await this.settingsRepository.saveSettings(settings)
  }

  /**
   * 检查设置是否存在
   */
  async hasSetting(key: string): Promise<boolean> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    return await this.settingsRepository.hasSetting(key)
  }

  /**
   * 获取设置的更新时间
   */
  async getSettingUpdatedAt(key: string): Promise<string | undefined> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    return await this.settingsRepository.getSettingUpdatedAt(key)
  }

  /**
   * 重置设置到默认值
   */
  async resetSetting(key: string, defaultValue?: any): Promise<void> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    if (defaultValue !== undefined) {
      await this.settingsRepository.saveSetting(key, defaultValue)
    } else {
      await this.settingsRepository.deleteSetting(key)
    }
  }

  /**
   * 获取应用程序默认设置
   */
  async getDefaultSettings(): Promise<Record<string, any>> {
    return {
      // 应用程序设置
      saveAppState: true,
      restoreTabs: true,
      autoSave: true,
      autoSaveInterval: 30000, // 30秒

      // 界面设置
      theme: 'light',
      fontSize: 14,
      fontFamily: 'system-ui',
      
      // 标题栏设置
      showTitlebar: true,
      enableVirtualization: true,
      maxVisibleTabs: 10,
      
      // 窗口设置
      rememberWindowState: true,
      startMaximized: false,
      
      // 编辑器设置
      wordWrap: true,
      lineNumbers: true,
      minimap: false,
      
      // 其他设置
      checkForUpdates: true,
      enableTelemetry: false
    }
  }

  /**
   * 初始化默认设置
   */
  async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = await this.getDefaultSettings()
    const existingSettings = await this.getAllSettings()

    // 只设置不存在的设置
    const settingsToSet: Record<string, any> = {}
    for (const [key, value] of Object.entries(defaultSettings)) {
      if (!(key in existingSettings)) {
        settingsToSet[key] = value
      }
    }

    if (Object.keys(settingsToSet).length > 0) {
      await this.saveSettings(settingsToSet)
    }
  }

  /**
   * 验证设置值
   */
  private validateSettingValue(value: any): void {
    // 检查是否可以序列化为JSON
    try {
      JSON.stringify(value)
    } catch (error) {
      throw new Error('Setting value must be JSON serializable')
    }

    // 检查值的大小（防止存储过大的数据）
    const serialized = JSON.stringify(value)
    if (serialized.length > 1024 * 1024) { // 1MB限制
      throw new Error('Setting value is too large (max 1MB)')
    }
  }
}
