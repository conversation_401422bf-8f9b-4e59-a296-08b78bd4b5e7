import { z } from 'zod'

// =============================================================================
// 核心类型定义 (使用 zod schema)
// =============================================================================

// 标签页类型枚举
export const TabTypeSchema = z.enum(['home', 'folder', 'file', 'welcome', 'settings'])

// 窗口边界信息
export const BoundsSchema = z.object({
  x: z.number().describe('窗口X坐标'),
  y: z.number().describe('窗口Y坐标'),
  width: z.number().min(1).describe('窗口宽度'),
  height: z.number().min(1).describe('窗口高度')
})

// 标签页 Schema
export const TabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.boolean().default(false).describe('是否为当前活跃标签页'),
  isDirty: z.boolean().default(false).describe('是否有未保存的更改'),
  isPinned: z.boolean().default(false).describe('是否为固定标签页'),
  icon: z.string().optional().describe('标签页图标路径或名称'),
  closable: z.boolean().default(true).describe('是否可关闭'),
  tooltip: z.string().optional().describe('鼠标悬停提示文本'),
  lastModified: z.date().optional().describe('最后修改时间'),
  metadata: z.record(z.any()).optional().describe('扩展元数据'),
  path: z.string().optional().describe('文件路径 (如果是文件标签页)'),
  type: TabTypeSchema.default('file').describe('标签页类型'),
  sortOrder: z.number().default(0).describe('标签页排序顺序')
})

// 窗口状态 Schema
export const WindowStateSchema = z.object({
  id: z.number().default(1).describe('窗口ID (通常为1，单窗口应用)'),
  isMaximized: z.boolean().default(false).describe('是否最大化'),
  isMinimized: z.boolean().default(false).describe('是否最小化'),
  isFullscreen: z.boolean().default(false).describe('是否全屏'),
  isFocused: z.boolean().default(true).describe('是否获得焦点'),
  bounds: BoundsSchema.optional().describe('窗口边界信息'),
  updatedAt: z.string().optional().describe('最后更新时间')
})

// 应用设置 Schema
export const AppSettingsSchema = z.object({
  saveAppState: z.boolean().default(true).describe('是否保存应用状态'),
  restoreWindowState: z.boolean().default(true).describe('是否恢复窗口状态'),
  restoreTabs: z.boolean().default(true).describe('是否恢复标签页'),
  maxRecentTabs: z.number().min(1).max(50).default(10).describe('最大最近标签页数量'),
  theme: z.enum(['light', 'dark', 'system']).default('system').describe('主题设置'),
  language: z.string().default('zh-CN').describe('语言设置')
})

// 标题栏配置 Schema
export const TitlebarConfigSchema = z.object({
  showIcon: z.boolean().default(true).describe('是否显示图标'),
  showTitle: z.boolean().default(true).describe('是否显示标题'),
  maxVisibleTabs: z.number().min(1).max(50).default(8).describe('最大可见标签页数量'),
  enableTabReordering: z.boolean().default(true).describe('是否启用标签页重排序'),
  enableTabContextMenu: z.boolean().default(true).describe('是否启用标签页右键菜单'),
  enableVirtualization: z.boolean().default(true).describe('是否启用虚拟化'),
  customStyles: z.record(z.string()).optional().describe('自定义样式')
})

// 最近访问文件 Schema
export const RecentFileSchema = z.object({
  id: z.number().optional().describe('自增主键'),
  path: z.string().min(1).describe('文件完整路径'),
  name: z.string().min(1).describe('文件名'),
  lastAccessed: z.string().describe('最后访问时间'),
  fileSize: z.number().optional().describe('文件大小 (字节)'),
  fileType: z.string().optional().describe('文件类型/扩展名'),
  metadata: z.record(z.any()).optional().describe('文件元数据')
})

// API响应 Schema
export const ApiResponseSchema = z.object({
  success: z.boolean().describe('操作是否成功'),
  data: z.any().optional().describe('响应数据'),
  error: z.string().optional().describe('错误信息'),
  timestamp: z
    .number()
    .default(() => Date.now())
    .describe('响应时间戳')
})

// =============================================================================
// 导出推断类型
// =============================================================================

export type Tab = z.infer<typeof TabSchema>
export type WindowState = z.infer<typeof WindowStateSchema>
export type AppSettings = z.infer<typeof AppSettingsSchema>
export type TitlebarConfig = z.infer<typeof TitlebarConfigSchema>
export type RecentFile = z.infer<typeof RecentFileSchema>
export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & { data?: T }
