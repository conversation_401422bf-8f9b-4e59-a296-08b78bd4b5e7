import { z } from 'zod'

// =============================================================================
// 基础类型定义 (使用 zod schema)
// =============================================================================

// 标签页类型枚举
export const TabTypeSchema = z.enum(['home', 'folder', 'file', 'welcome', 'settings'])

// 窗口边界信息
export const BoundsSchema = z.object({
  x: z.number().describe('窗口X坐标'),
  y: z.number().describe('窗口Y坐标'),
  width: z.number().min(1).describe('窗口宽度'),
  height: z.number().min(1).describe('窗口高度')
})

// 标签页 Schema (业务层使用)
export const TabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.boolean().default(false).describe('是否为当前活跃标签页'),
  isDirty: z.boolean().default(false).describe('是否有未保存的更改'),
  isPinned: z.boolean().default(false).describe('是否为固定标签页'),
  icon: z.string().optional().describe('标签页图标路径或名称'),
  closable: z.boolean().default(true).describe('是否可关闭'),
  tooltip: z.string().optional().describe('鼠标悬停提示文本'),
  lastModified: z.date().optional().describe('最后修改时间'),
  metadata: z.record(z.any()).optional().describe('扩展元数据'),
  path: z.string().optional().describe('文件路径 (如果是文件标签页)'),
  type: TabTypeSchema.default('file').describe('标签页类型'),
  sortOrder: z.number().default(0).describe('标签页排序顺序')
})

// 窗口状态 Schema
export const WindowStateSchema = z.object({
  id: z.number().default(1).describe('窗口ID (通常为1，单窗口应用)'),
  isMaximized: z.boolean().default(false).describe('是否最大化'),
  isMinimized: z.boolean().default(false).describe('是否最小化'),
  isFullscreen: z.boolean().default(false).describe('是否全屏'),
  isFocused: z.boolean().default(true).describe('是否获得焦点'),
  bounds: BoundsSchema.optional().describe('窗口边界信息'),
  updatedAt: z.string().optional().describe('最后更新时间')
})

// 应用设置 Schema
export const AppSettingsSchema = z.object({
  saveAppState: z.boolean().default(true).describe('是否保存应用状态'),
  restoreWindowState: z.boolean().default(true).describe('是否恢复窗口状态'),
  restoreTabs: z.boolean().default(true).describe('是否恢复标签页'),
  maxRecentTabs: z.number().min(1).max(50).default(10).describe('最大最近标签页数量'),
  theme: z.enum(['light', 'dark', 'system']).default('system').describe('主题设置'),
  language: z.string().default('zh-CN').describe('语言设置')
})

// 标题栏配置 Schema
export const TitlebarConfigSchema = z.object({
  showIcon: z.boolean().default(true).describe('是否显示图标'),
  showTitle: z.boolean().default(true).describe('是否显示标题'),
  maxVisibleTabs: z.number().min(1).max(50).default(8).describe('最大可见标签页数量'),
  enableTabReordering: z.boolean().default(true).describe('是否启用标签页重排序'),
  enableTabContextMenu: z.boolean().default(true).describe('是否启用标签页右键菜单'),
  enableVirtualization: z.boolean().default(true).describe('是否启用虚拟化'),
  customStyles: z.record(z.string()).optional().describe('自定义样式')
})

// 最近访问文件 Schema
export const RecentFileSchema = z.object({
  id: z.number().optional().describe('自增主键'),
  path: z.string().min(1).describe('文件完整路径'),
  name: z.string().min(1).describe('文件名'),
  lastAccessed: z.string().describe('最后访问时间'),
  fileSize: z.number().optional().describe('文件大小 (字节)'),
  fileType: z.string().optional().describe('文件类型/扩展名'),
  metadata: z.record(z.any()).optional().describe('文件元数据')
})

// API响应 Schema
export const ApiResponseSchema = z.object({
  success: z.boolean().describe('操作是否成功'),
  data: z.any().optional().describe('响应数据'),
  error: z.string().optional().describe('错误信息'),
  timestamp: z
    .number()
    .default(() => Date.now())
    .describe('响应时间戳')
})

// =============================================================================
// 数据库层类型定义 (SQLite 存储格式)
// =============================================================================

// 数据库标签页 Schema (SQLite 中布尔值存储为 0/1)
export const DatabaseTabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.number().min(0).max(1).describe('是否为当前活跃标签页 (0=否, 1=是)'),
  isDirty: z.number().min(0).max(1).describe('是否有未保存的更改 (0=否, 1=是)'),
  isPinned: z.number().min(0).max(1).describe('是否为固定标签页 (0=否, 1=是)'),
  icon: z.string().optional().describe('标签页图标路径或名称'),
  closable: z.number().min(0).max(1).describe('是否可关闭 (0=不可关闭, 1=可关闭)'),
  tooltip: z.string().optional().describe('鼠标悬停提示文本'),
  lastModified: z.string().optional().describe('最后修改时间 (ISO字符串)'),
  metadata: z.string().optional().describe('扩展元数据 (JSON字符串)'),
  path: z.string().optional().describe('文件路径 (如果是文件标签页)'),
  type: z.string().describe('标签页类型'),
  sort_order: z.number().default(0).describe('标签页排序顺序'),
  createdAt: z.string().describe('创建时间 (ISO字符串)'),
  updatedAt: z.string().describe('最后更新时间 (ISO字符串)')
})

// 数据库设置 Schema
export const DatabaseSettingsSchema = z.object({
  key: z.string().min(1).describe('设置项键名'),
  value: z.string().describe('设置项值 (JSON字符串)'),
  updatedAt: z.string().describe('最后更新时间 (ISO字符串)')
})

// 数据库窗口状态 Schema
export const DatabaseWindowStateSchema = z.object({
  id: z.number().default(1).describe('窗口ID (通常为1，单窗口应用)'),
  isMaximized: z.number().min(0).max(1).describe('是否最大化 (0=否, 1=是)'),
  isMinimized: z.number().min(0).max(1).describe('是否最小化 (0=否, 1=是)'),
  isFullscreen: z.number().min(0).max(1).describe('是否全屏 (0=否, 1=是)'),
  isFocused: z.number().min(0).max(1).describe('是否获得焦点 (0=否, 1=是)'),
  bounds: z.string().optional().describe('窗口边界信息 (JSON字符串)'),
  updatedAt: z.string().describe('最后更新时间 (ISO字符串)')
})

// 数据库最近文件 Schema
export const DatabaseRecentFileSchema = z.object({
  id: z.number().optional().describe('自增主键'),
  path: z.string().min(1).describe('文件完整路径'),
  name: z.string().min(1).describe('文件名'),
  lastAccessed: z.string().describe('最后访问时间 (ISO字符串)'),
  fileSize: z.number().optional().describe('文件大小 (字节)'),
  fileType: z.string().optional().describe('文件类型/扩展名'),
  metadata: z.string().optional().describe('文件元数据 (JSON字符串)')
})

// =============================================================================
// 业务层类型定义 (应用程序使用)
// =============================================================================

// 服务层标签页 Schema (使用布尔值，更符合业务逻辑)
export const ServiceTabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.boolean().describe('是否为当前活跃标签页'),
  isDirty: z.boolean().describe('是否有未保存的更改'),
  isPinned: z.boolean().describe('是否为固定标签页'),
  icon: z.string().optional().describe('标签页图标路径或名称'),
  closable: z.boolean().describe('是否可关闭'),
  tooltip: z.string().optional().describe('鼠标悬停提示文本'),
  lastModified: z.string().optional().describe('最后修改时间 (ISO字符串)'),
  metadata: z.string().optional().describe('扩展元数据 (JSON字符串)'),
  path: z.string().optional().describe('文件路径 (如果是文件标签页)'),
  type: z.string().describe('标签页类型'),
  sort_order: z.number().default(0).describe('标签页排序顺序'),
  createdAt: z.string().describe('创建时间 (ISO字符串)'),
  updatedAt: z.string().describe('最后更新时间 (ISO字符串)')
})

// 服务层窗口状态 Schema
export const ServiceWindowStateSchema = z.object({
  id: z.number().default(1).describe('窗口ID (通常为1，单窗口应用)'),
  isMaximized: z.boolean().describe('是否最大化'),
  isMinimized: z.boolean().describe('是否最小化'),
  isFullscreen: z.boolean().describe('是否全屏'),
  isFocused: z.boolean().describe('是否获得焦点'),
  bounds: z.string().optional().describe('窗口边界信息 (JSON字符串)'),
  updatedAt: z.string().describe('最后更新时间 (ISO字符串)')
})

// =============================================================================
// 导出推断类型
// =============================================================================

export type Tab = z.infer<typeof TabSchema>
export type WindowState = z.infer<typeof WindowStateSchema>
export type AppSettings = z.infer<typeof AppSettingsSchema>
export type TitlebarConfig = z.infer<typeof TitlebarConfigSchema>
export type RecentFile = z.infer<typeof RecentFileSchema>

// 数据库层类型
export type DatabaseTab = z.infer<typeof DatabaseTabSchema>
export type DatabaseSettings = z.infer<typeof DatabaseSettingsSchema>
export type DatabaseWindowState = z.infer<typeof DatabaseWindowStateSchema>
export type DatabaseRecentFile = z.infer<typeof DatabaseRecentFileSchema>

// 服务层类型
export type ServiceTab = z.infer<typeof ServiceTabSchema>
export type ServiceWindowState = z.infer<typeof ServiceWindowStateSchema>

// 修复ApiResponse类型，确保timestamp总是存在
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: number
}

// 创建ApiResponse的辅助函数
export function createApiResponse<T = any>(
  success: boolean,
  data?: T,
  error?: string
): ApiResponse<T> {
  return {
    success,
    data,
    error,
    timestamp: Date.now()
  }
}
