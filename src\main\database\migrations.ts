import type Database from 'better-sqlite3'

interface Migration {
  version: number
  name: string
  up: (db: Database.Database) => void
}

const migrations: Migration[] = [
  {
    version: 1,
    name: 'initial_schema',
    up: (db) => {
      // 创建数据库版本管理表
      db.exec(`
        CREATE TABLE IF NOT EXISTS schema_version (
          version INTEGER PRIMARY KEY,                    -- 数据库版本号
          applied_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP  -- 迁移应用时间
        )
      `)

      // 创建标签页管理表
      db.exec(`
        CREATE TABLE IF NOT EXISTS tabs (
          id TEXT PRIMARY KEY,                            -- 标签页唯一标识符
          title TEXT NOT NULL,                            -- 标签页显示标题
          active INTEGER NOT NULL DEFAULT 0,              -- 是否为当前活跃标签页 (0=否, 1=是)
          isDirty INTEGER NOT NULL DEFAULT 0,             -- 是否有未保存的更改 (0=否, 1=是)
          isPinned INTEGER NOT NULL DEFAULT 0,            -- 是否为固定标签页 (0=否, 1=是)
          icon TEXT,                                       -- 标签页图标路径或名称
          closable INTEGER NOT NULL DEFAULT 1,            -- 是否可关闭 (0=不可关闭, 1=可关闭)
          tooltip TEXT,                                    -- 鼠标悬停提示文本
          lastModified TEXT,                               -- 最后修改时间
          metadata TEXT,                                   -- 扩展元数据 (JSON格式)
          path TEXT,                                       -- 文件路径 (如果是文件标签页)
          type TEXT NOT NULL DEFAULT 'file',              -- 标签页类型 (file=文件, welcome=欢迎页, settings=设置等)
          sort_order INTEGER DEFAULT 0,                   -- 标签页排序顺序
          createdAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
          updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP   -- 最后更新时间
        )
      `)

      // 创建应用设置表
      db.exec(`
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,                            -- 设置项键名
          value TEXT NOT NULL,                             -- 设置项值 (JSON格式)
          updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP   -- 最后更新时间
        )
      `)

      // 创建窗口状态表
      db.exec(`
        CREATE TABLE IF NOT EXISTS window_state (
          id INTEGER PRIMARY KEY DEFAULT 1,               -- 窗口ID (通常为1，单窗口应用)
          isMaximized INTEGER NOT NULL DEFAULT 0,         -- 是否最大化 (0=否, 1=是)
          isMinimized INTEGER NOT NULL DEFAULT 0,         -- 是否最小化 (0=否, 1=是)
          isFullscreen INTEGER NOT NULL DEFAULT 0,        -- 是否全屏 (0=否, 1=是)
          isFocused INTEGER NOT NULL DEFAULT 1,           -- 是否获得焦点 (0=否, 1=是)
          bounds TEXT,                                     -- 窗口边界信息 (JSON格式: {x, y, width, height})
          updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP   -- 最后更新时间
        )
      `)

      // 创建最近访问文件表
      db.exec(`
        CREATE TABLE IF NOT EXISTS recent_files (
          id INTEGER PRIMARY KEY AUTOINCREMENT,           -- 自增主键
          path TEXT NOT NULL UNIQUE,                      -- 文件完整路径 (唯一)
          name TEXT NOT NULL,                             -- 文件名
          lastAccessed TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 最后访问时间
          fileSize INTEGER,                               -- 文件大小 (字节)
          fileType TEXT,                                  -- 文件类型/扩展名
          metadata TEXT                                   -- 文件元数据 (JSON格式)
        )
      `)

      // 创建数据库索引以提升查询性能
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_tabs_active ON tabs(active);
        CREATE INDEX IF NOT EXISTS idx_tabs_type ON tabs(type);
        CREATE INDEX IF NOT EXISTS idx_tabs_sort_order ON tabs(sort_order);
        CREATE INDEX IF NOT EXISTS idx_tabs_updated ON tabs(updatedAt);
        CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);
        CREATE INDEX IF NOT EXISTS idx_recent_files_path ON recent_files(path);
        CREATE INDEX IF NOT EXISTS idx_recent_files_accessed ON recent_files(lastAccessed);
      `)
    }
  }
]

export async function runMigrations(db: Database.Database): Promise<void> {
  try {
    // 获取当前数据库版本
    let currentVersion = 0
    try {
      const result = db.prepare('SELECT MAX(version) as version FROM schema_version').get() as {
        version: number | null
      }
      currentVersion = result?.version || 0
    } catch (error) {
      console.error('Failed to get database version:', error)
      // 如果表不存在，版本为0
      console.log('First run, initializing database...')
    }

    console.log(`Current database version: ${currentVersion}`)

    // 运行需要的迁移
    const pendingMigrations = migrations.filter((m) => m.version > currentVersion)

    if (pendingMigrations.length === 0) {
      console.log('Database is already up to date')
      return
    }

    console.log(`Need to run ${pendingMigrations.length} migrations`)

    // 在事务中运行所有迁移
    const transaction = db.transaction(() => {
      for (const migration of pendingMigrations) {
        console.log(`Running migration: ${migration.name} (version ${migration.version})`)

        try {
          migration.up(db)

          // 记录迁移版本
          db.prepare('INSERT INTO schema_version (version) VALUES (?)').run(migration.version)

          console.log(`Migration ${migration.name} completed`)
        } catch (error) {
          console.error(`Migration ${migration.name} failed:`, error)
          throw error
        }
      }
    })

    transaction()

    console.log('All database migrations completed')
  } catch (error) {
    console.error('Database migration failed:', error)
    throw error
  }
}
