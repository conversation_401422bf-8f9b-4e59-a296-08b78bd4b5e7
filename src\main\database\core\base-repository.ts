import type Database from 'better-sqlite3'
import CoreDatabaseManager from './database-manager'

/**
 * 基础仓库类
 * 提供通用的数据库访问功能
 */
export abstract class BaseRepository {
  protected get db(): Database.Database {
    const dbManager = CoreDatabaseManager.getInstance()
    return dbManager.getDatabase()
  }

  /**
   * 执行事务
   */
  protected transaction<T>(fn: () => T): T {
    const transaction = this.db.transaction(fn)
    return transaction()
  }

  /**
   * 执行异步事务
   */
  protected async asyncTransaction<T>(fn: () => Promise<T>): Promise<T> {
    // 对于 better-sqlite3，我们需要将异步操作包装在同步事务中
    return new Promise((resolve, reject) => {
      try {
        const transaction = this.db.transaction(() => {
          fn().then(resolve).catch(reject)
        })
        transaction()
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 检查表是否存在
   */
  protected tableExists(tableName: string): boolean {
    const stmt = this.db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name=?
    `)
    return stmt.get(tableName) !== undefined
  }

  /**
   * 获取表的行数
   */
  protected getTableRowCount(tableName: string): number {
    if (!this.tableExists(tableName)) {
      return 0
    }
    const stmt = this.db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`)
    const result = stmt.get() as { count: number }
    return result.count
  }

  /**
   * 清空表
   */
  protected truncateTable(tableName: string): void {
    if (this.tableExists(tableName)) {
      this.db.prepare(`DELETE FROM ${tableName}`).run()
    }
  }

  /**
   * 获取表的最后更新时间
   */
  protected getLastUpdated(tableName: string, timeColumn = 'updatedAt'): string | null {
    if (!this.tableExists(tableName)) {
      return null
    }

    try {
      const stmt = this.db.prepare(`
        SELECT MAX(${timeColumn}) as lastUpdated 
        FROM ${tableName}
      `)
      const result = stmt.get() as { lastUpdated: string | null }
      return result.lastUpdated
    } catch (error) {
      console.error(`Failed to get last updated time for table ${tableName}:`, error)
      return null
    }
  }

  /**
   * 批量插入数据
   */
  protected batchInsert<T extends Record<string, any>>(
    tableName: string,
    data: T[],
    onConflict: 'IGNORE' | 'REPLACE' = 'IGNORE'
  ): void {
    if (data.length === 0) return

    const columns = Object.keys(data[0])
    const placeholders = columns.map(() => '?').join(', ')
    const columnNames = columns.join(', ')

    const stmt = this.db.prepare(`
      INSERT OR ${onConflict} INTO ${tableName} (${columnNames})
      VALUES (${placeholders})
    `)

    this.transaction(() => {
      for (const item of data) {
        const values = columns.map((col) => item[col])
        stmt.run(...values)
      }
    })
  }

  /**
   * 批量更新数据
   */
  protected batchUpdate<T extends Record<string, any>>(
    tableName: string,
    data: T[],
    keyColumn: string
  ): void {
    if (data.length === 0) return

    this.transaction(() => {
      for (const item of data) {
        const { [keyColumn]: keyValue, ...updates } = item
        const columns = Object.keys(updates)
        const setClause = columns.map((col) => `${col} = ?`).join(', ')
        const values = columns.map((col) => updates[col])

        const stmt = this.db.prepare(`
          UPDATE ${tableName} 
          SET ${setClause} 
          WHERE ${keyColumn} = ?
        `)
        stmt.run(...values, keyValue)
      }
    })
  }

  /**
   * 删除旧记录
   */
  protected deleteOldRecords(tableName: string, timeColumn: string, daysToKeep: number): number {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

    const stmt = this.db.prepare(`
      DELETE FROM ${tableName} 
      WHERE ${timeColumn} < ?
    `)
    const result = stmt.run(cutoffDate.toISOString())
    return result.changes
  }

  /**
   * 获取数据库信息
   */
  protected getDatabaseInfo(): {
    version: string
    pageSize: number
    pageCount: number
    freePages: number
  } {
    const version = this.db.pragma('user_version', { simple: true }) as number
    const pageSize = this.db.pragma('page_size', { simple: true }) as number
    const pageCount = this.db.pragma('page_count', { simple: true }) as number
    const freePages = this.db.pragma('freelist_count', { simple: true }) as number

    return {
      version: version.toString(),
      pageSize,
      pageCount,
      freePages
    }
  }
}
