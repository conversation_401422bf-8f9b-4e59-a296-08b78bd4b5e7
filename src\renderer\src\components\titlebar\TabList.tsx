import React, { useCallback } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { TabItem } from './TabItem'
import { useTabManager } from '@/hooks/useTabManager'
import type { Tab, TitlebarConfig } from '@/types'

interface TabListProps {
  tabs: Tab[]
  config: TitlebarConfig
  onTabAction: (action: string, tabId: string, payload?: any) => void
  className?: string
}

const TAB_WIDTH = 200

export function TabList({ tabs, config, onTabAction, className }: TabListProps) {
  const { visibleRange, canScrollLeft, canScrollRight, scrollLeft, scrollRight } = useTabManager(
    tabs,
    config.maxVisibleTabs
  )

  const handleScroll = useCallback(
    (direction: 'left' | 'right') => {
      if (direction === 'left') {
        scrollLeft()
      } else {
        scrollRight()
      }
    },
    [scrollLeft, scrollRight]
  )

  // 如果不需要虚拟化或标签页数量较少，直接渲染所有标签页
  if (!config.enableVirtualization || tabs.length <= config.maxVisibleTabs) {
    return (
      <div className={cn('flex items-center h-full overflow-hidden', className)}>
        <div className="flex-1 flex overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => (
            <TabItem
              key={tab.id}
              tab={tab}
              onAction={onTabAction}
              config={config}
              width={TAB_WIDTH}
            />
          ))}
        </div>
      </div>
    )
  }

  // 计算可见标签页
  const visibleTabs = tabs.slice(visibleRange.start, visibleRange.end)

  return (
    <div className={cn('flex items-center h-full', className)}>
      {/* 左滚动按钮 */}
      {canScrollLeft && (
        <button
          onClick={() => handleScroll('left')}
          className="h-8 w-8 p-0 shrink-0 mx-1 hover:bg-gray-100 rounded flex items-center justify-center"
          aria-label="向左滚动标签页"
          style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
        >
          <ChevronLeft className="h-4 w-4" />
        </button>
      )}

      {/* 标签页列表 */}
      <div className="flex-1 h-full flex overflow-hidden">
        {visibleTabs.map((tab) => (
          <TabItem
            key={tab.id}
            tab={tab}
            onAction={onTabAction}
            config={config}
            width={TAB_WIDTH}
          />
        ))}
      </div>

      {/* 右滚动按钮 */}
      {canScrollRight && (
        <button
          onClick={() => handleScroll('right')}
          className="h-8 w-8 p-0 shrink-0 mx-1 hover:bg-gray-100 rounded flex items-center justify-center"
          aria-label="向右滚动标签页"
          style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
        >
          <ChevronRight className="h-4 w-4" />
        </button>
      )}
    </div>
  )
}
