import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import type { WindowState, ApiResponse, Tab, AppSettings } from '../shared/types'

// Custom APIs for renderer
const api = {
  // 窗口控制
  window: {
    minimize: (): Promise<ApiResponse> => ipcRenderer.invoke('window:minimize'),
    maximize: (): Promise<ApiResponse<{ isMaximized: boolean }>> =>
      ipcRenderer.invoke('window:maximize'),
    close: (): Promise<ApiResponse> => ipcRenderer.invoke('window:close'),
    getState: (): Promise<ApiResponse<WindowState>> => ipcRenderer.invoke('window:get-state'),
    setTitle: (title: string): Promise<ApiResponse> =>
      ipcRenderer.invoke('window:set-title', title),
    setBounds: (bounds: {
      x: number
      y: number
      width: number
      height: number
    }): Promise<ApiResponse> => ipcRenderer.invoke('window:set-bounds', bounds),
    center: (): Promise<ApiResponse> => ipcRenderer.invoke('window:center'),
    onStateChanged: (callback: (state: Partial<WindowState>) => void) => {
      const handler = (_: any, state: Partial<WindowState>) => callback(state)
      ipcRenderer.on('window:state-changed', handler)
      return () => ipcRenderer.removeListener('window:state-changed', handler)
    }
  },

  // 标题栏功能
  titlebar: {
    showContextMenu: (x: number, y: number): Promise<ApiResponse> =>
      ipcRenderer.invoke('titlebar:show-context-menu', x, y)
  },

  // 数据库操作
  database: {
    saveTabs: (tabs: Tab[]): Promise<ApiResponse> => ipcRenderer.invoke('db:save-tabs', tabs),
    getTabs: (): Promise<ApiResponse<Tab[]>> => ipcRenderer.invoke('db:get-tabs'),
    saveSetting: (key: string, value: any): Promise<ApiResponse> =>
      ipcRenderer.invoke('db:save-setting', key, value),
    getSetting: (key: string, defaultValue?: any): Promise<ApiResponse<any>> =>
      ipcRenderer.invoke('db:get-setting', key, defaultValue),
    getAllSettings: (): Promise<ApiResponse<AppSettings>> =>
      ipcRenderer.invoke('db:get-all-settings'),
    saveWindowState: (state: WindowState): Promise<ApiResponse> =>
      ipcRenderer.invoke('db:save-window-state', state),
    getWindowState: (): Promise<ApiResponse<WindowState | null>> =>
      ipcRenderer.invoke('db:get-window-state'),
    cleanup: (): Promise<ApiResponse> => ipcRenderer.invoke('db:cleanup')
  },

  // 系统信息
  system: {
    getPlatform: () => process.platform,
    getVersions: () => process.versions
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
