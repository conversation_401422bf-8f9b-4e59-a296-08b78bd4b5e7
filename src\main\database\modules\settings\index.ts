/**
 * 设置模块
 * 提供设置相关的所有功能
 */

import { SettingsRepository } from './repository'
import { SettingsService } from './service'

// 导出单例实例
export const settingsRepository = SettingsRepository.getInstance()
export const settingsService = SettingsService.getInstance()

// 导出类型（如果需要）
export { SettingsRepository, SettingsService }

// 导出便捷方法
export const settings = {
  // 基础操作
  save: settingsService.saveSetting.bind(settingsService),
  get: settingsService.getSetting.bind(settingsService),
  getAll: settingsService.getAllSettings.bind(settingsService),
  delete: settingsService.deleteSetting.bind(settingsService),
  has: settingsService.hasSetting.bind(settingsService),

  // 批量操作
  saveMany: settingsService.saveSettings.bind(settingsService),
  deleteMany: settingsService.deleteSettings.bind(settingsService),

  // 前缀操作
  getByPrefix: settingsService.getSettingsByPrefix.bind(settingsService),
  deleteByPrefix: settingsService.deleteSettingsByPrefix.bind(settingsService),

  // 高级功能
  reset: settingsService.resetSetting.bind(settingsService),
  search: settingsService.searchSettings.bind(settingsService),
  getUpdatedAt: settingsService.getSettingUpdatedAt.bind(settingsService),

  // 默认设置
  getDefaults: settingsService.getDefaultSettings.bind(settingsService),
  initDefaults: settingsService.initializeDefaultSettings.bind(settingsService),

  // 维护操作
  getStats: settingsService.getSettingsStats.bind(settingsService),
  cleanup: settingsService.cleanupEmptySettings.bind(settingsService)
}

// 默认导出便捷对象
export default settings
