/**
 * 设置模块
 * 提供设置相关的所有功能
 */

import { SettingsService } from './service'

// 获取服务实例
const settingsService = SettingsService.getInstance()

// 导出便捷方法
const settings = {
  // 基础操作
  save: settingsService.saveSetting.bind(settingsService),
  get: settingsService.getSetting.bind(settingsService),
  getAll: settingsService.getAllSettings.bind(settingsService),
  delete: settingsService.deleteSetting.bind(settingsService),

  // 批量操作
  saveMany: settingsService.saveSettings.bind(settingsService),
  deleteMany: settingsService.deleteSettings.bind(settingsService),

  // 默认设置
  initDefaults: settingsService.initializeDefaultSettings.bind(settingsService),

  // 维护操作
  cleanup: settingsService.cleanupEmptySettings.bind(settingsService)
}

// 默认导出便捷对象
export default settings
