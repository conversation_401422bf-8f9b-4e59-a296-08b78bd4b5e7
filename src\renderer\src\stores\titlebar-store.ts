import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { TabSchema, TitlebarConfigSchema, type Tab, type TitlebarConfig } from '@/types'

interface TitlebarState {
  tabs: Tab[]
  config: TitlebarConfig

  // Actions
  addTab: (tab: Omit<Tab, 'active'>) => void
  removeTab: (id: string) => void
  updateTab: (id: string, updates: Partial<Tab>) => void
  reorderTabs: (fromId: string, toId: string) => void
  duplicateTab: (id: string) => void
  closeOtherTabs: (id: string) => void
  closeTabsToRight: (id: string) => void
  pinTab: (id: string) => void
  unpinTab: (id: string) => void
  updateConfig: (config: Partial<TitlebarConfig>) => void
  loadTabs: (tabs: Tab[]) => void
  saveTabs: () => Promise<void>
}

export const useTitlebarStore = create<TitlebarState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      tabs: [],
      config: TitlebarConfigSchema.parse({}),

      addTab: (tabData) => {
        const tab = TabSchema.parse(tabData)

        set((state) => {
          // 取消其他标签页的激活状态
          state.tabs.forEach((t) => (t.active = false))

          // 添加新标签页
          state.tabs.push({ ...tab, active: true })
        })
      },

      removeTab: (id) => {
        set((state) => {
          const index = state.tabs.findIndex((tab) => tab.id === id)
          if (index === -1) return

          const wasActive = state.tabs[index].active
          const isHome = state.tabs[index].type === 'home'

          // 首页标签页不能关闭
          if (isHome) return

          state.tabs.splice(index, 1)

          // 如果删除的是活跃标签页，激活相邻的标签页
          if (wasActive && state.tabs.length > 0) {
            const nextIndex = Math.min(index, state.tabs.length - 1)
            state.tabs[nextIndex].active = true
          }
        })
      },

      updateTab: (id, updates) => {
        set((state) => {
          const tab = state.tabs.find((t) => t.id === id)
          if (!tab) return

          // 如果要激活这个标签页，先取消其他标签页的激活状态
          if (updates.active === true) {
            state.tabs.forEach((t) => (t.active = false))
          }

          Object.assign(tab, updates)
        })
      },

      reorderTabs: (fromId, toId) => {
        set((state) => {
          const fromIndex = state.tabs.findIndex((tab) => tab.id === fromId)
          const toIndex = state.tabs.findIndex((tab) => tab.id === toId)

          if (fromIndex === -1 || toIndex === -1) return

          // 不允许移动首页标签页
          if (state.tabs[fromIndex].type === 'home') return

          const [movedTab] = state.tabs.splice(fromIndex, 1)
          state.tabs.splice(toIndex, 0, movedTab)
        })
      },

      duplicateTab: (id) => {
        set((state) => {
          const original = state.tabs.find((tab) => tab.id === id)
          if (!original) return

          // 首页标签页不能复制
          if (original.type === 'home') return

          const duplicate: Tab = {
            ...original,
            id: `${original.id}-copy-${Date.now()}`,
            title: `${original.title} (副本)`,
            active: false,
            isDirty: false
          }

          const index = state.tabs.findIndex((tab) => tab.id === id)
          state.tabs.splice(index + 1, 0, duplicate)
        })
      },

      closeOtherTabs: (id) => {
        set((state) => {
          const keepTab = state.tabs.find((tab) => tab.id === id)
          if (!keepTab) return

          // 保留首页和指定的标签页
          const homeTab = state.tabs.find((tab) => tab.type === 'home')
          const tabsToKeep = [homeTab, keepTab].filter(Boolean) as Tab[]

          state.tabs = tabsToKeep.map((tab) => ({
            ...tab,
            active: tab.id === id
          }))
        })
      },

      closeTabsToRight: (id) => {
        set((state) => {
          const index = state.tabs.findIndex((tab) => tab.id === id)
          if (index === -1) return

          // 保留首页标签页
          const homeTab = state.tabs.find((tab) => tab.type === 'home')
          const leftTabs = state.tabs.slice(0, index + 1)

          if (homeTab && !leftTabs.includes(homeTab)) {
            state.tabs = [homeTab, ...leftTabs]
          } else {
            state.tabs = leftTabs
          }
        })
      },

      pinTab: (id) => {
        set((state) => {
          const tab = state.tabs.find((t) => t.id === id)
          if (tab && tab.type !== 'home') {
            tab.isPinned = true
            tab.closable = false

            // 将固定的标签页移到前面（首页后面）
            const index = state.tabs.findIndex((t) => t.id === id)
            const [pinnedTab] = state.tabs.splice(index, 1)

            // 找到第一个非首页非固定标签页的位置
            const insertIndex = state.tabs.findIndex((t) => t.type !== 'home' && !t.isPinned)
            state.tabs.splice(insertIndex === -1 ? state.tabs.length : insertIndex, 0, pinnedTab)
          }
        })
      },

      unpinTab: (id) => {
        set((state) => {
          const tab = state.tabs.find((t) => t.id === id)
          if (tab) {
            tab.isPinned = false
            tab.closable = true
          }
        })
      },

      updateConfig: (configUpdates) => {
        set((state) => {
          state.config = { ...state.config, ...configUpdates }
        })
      },

      loadTabs: (tabs) => {
        set((state) => {
          state.tabs = tabs
        })
      },

      saveTabs: async () => {
        const { tabs } = get()
        try {
          await window.api.database.saveTabs(tabs)
        } catch (error) {
          console.error('Failed to save tabs:', error)
        }
      }
    }))
  )
)

// 订阅标签页变化，自动保存到数据库
useTitlebarStore.subscribe(
  (state) => state.tabs,
  async (tabs) => {
    try {
      await window.api.database.saveTabs(tabs)
    } catch (error) {
      console.error('Failed to auto-save tabs:', error)
    }
  }
)
