import type { DatabaseTab } from '../../../../shared/types'
import { BaseRepository } from '../../core/base-repository'

/**
 * 标签页数据仓库
 */
export class TabRepository extends BaseRepository {
  private static instance: TabRepository

  static getInstance(): TabRepository {
    if (!TabRepository.instance) {
      TabRepository.instance = new TabRepository()
    }
    return TabRepository.instance
  }

  /**
   * 保存标签页数据（替换所有）
   */
  saveTabs(tabs: DatabaseTab[]): void {
    this.transaction(() => {
      // 清空现有标签页
      this.truncateTable('tabs')

      if (tabs.length === 0) return

      // 准备插入语句
      const stmt = this.db.prepare(`
        INSERT INTO tabs (
          id, title, active, isDirty, isPinned, icon, closable,
          tooltip, lastModified, metadata, path, type, sort_order, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      // 批量插入标签页
      for (const tab of tabs) {
        stmt.run(
          tab.id,
          tab.title,
          tab.active ? 1 : 0,
          tab.isDirty ? 1 : 0,
          tab.isPinned ? 1 : 0,
          tab.icon,
          tab.closable ? 1 : 0,
          tab.tooltip,
          tab.lastModified,
          tab.metadata,
          tab.path,
          tab.type,
          tab.sort_order || 0,
          tab.createdAt,
          tab.updatedAt
        )
      }
    })
  }

  /**
   * 获取所有标签页
   */
  getTabs(): DatabaseTab[] {
    const stmt = this.db.prepare('SELECT * FROM tabs ORDER BY createdAt ASC')
    return stmt.all() as DatabaseTab[]
  }

  /**
   * 根据ID获取标签页
   */
  getTabById(id: string): DatabaseTab | undefined {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE id = ?')
    return stmt.get(id) as DatabaseTab | undefined
  }

  /**
   * 插入新标签页
   */
  insertTab(tab: DatabaseTab): void {
    const stmt = this.db.prepare(`
      INSERT INTO tabs (
        id, title, active, isDirty, isPinned, icon, closable,
        tooltip, lastModified, metadata, path, type, sort_order, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
      tab.id,
      tab.title,
      tab.active ? 1 : 0,
      tab.isDirty ? 1 : 0,
      tab.isPinned ? 1 : 0,
      tab.icon,
      tab.closable ? 1 : 0,
      tab.tooltip,
      tab.lastModified,
      tab.metadata,
      tab.path,
      tab.type,
      tab.sort_order || 0,
      tab.createdAt,
      tab.updatedAt
    )
  }

  /**
   * 更新标签页
   */
  updateTab(id: string, updates: Partial<DatabaseTab>): void {
    const fields = Object.keys(updates).filter((key) => key !== 'id')
    if (fields.length === 0) return

    const setClause = fields.map((field) => `${field} = ?`).join(', ')
    const values = fields.map((field) => updates[field as keyof DatabaseTab])

    const stmt = this.db.prepare(`
      UPDATE tabs SET ${setClause}, updatedAt = ? WHERE id = ?
    `)

    stmt.run(...values, new Date().toISOString(), id)
  }

  /**
   * 删除标签页
   */
  deleteTab(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM tabs WHERE id = ?')
    const result = stmt.run(id)
    return result.changes > 0
  }

  /**
   * 获取活跃标签页
   */
  getActiveTab(): DatabaseTab | undefined {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE active = 1 LIMIT 1')
    return stmt.get() as DatabaseTab | undefined
  }

  /**
   * 设置活跃标签页
   */
  setActiveTab(id: string): void {
    this.transaction(() => {
      // 取消所有标签页的活跃状态
      this.db.prepare('UPDATE tabs SET active = 0').run()

      // 设置指定标签页为活跃
      this.db
        .prepare('UPDATE tabs SET active = 1, updatedAt = ? WHERE id = ?')
        .run(new Date().toISOString(), id)
    })
  }

  /**
   * 获取固定标签页
   */
  getPinnedTabs(): DatabaseTab[] {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE isPinned = 1 ORDER BY createdAt ASC')
    return stmt.all() as DatabaseTab[]
  }

  /**
   * 获取未保存的标签页
   */
  getDirtyTabs(): DatabaseTab[] {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE isDirty = 1 ORDER BY createdAt ASC')
    return stmt.all() as DatabaseTab[]
  }

  /**
   * 根据类型获取标签页
   */
  getTabsByType(type: string): DatabaseTab[] {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE type = ? ORDER BY createdAt ASC')
    return stmt.all(type) as DatabaseTab[]
  }

  /**
   * 清理旧的标签页记录
   */
  cleanupOldTabs(daysToKeep: number = 30): number {
    return this.deleteOldRecords('tabs', 'updatedAt', daysToKeep)
  }

  /**
   * 获取标签页统计信息
   */
  getTabStats(): {
    total: number
    active: number
    pinned: number
    dirty: number
    byType: Record<string, number>
  } {
    const total = this.getTableRowCount('tabs')

    const activeStmt = this.db.prepare('SELECT COUNT(*) as count FROM tabs WHERE active = 1')
    const active = (activeStmt.get() as { count: number }).count

    const pinnedStmt = this.db.prepare('SELECT COUNT(*) as count FROM tabs WHERE isPinned = 1')
    const pinned = (pinnedStmt.get() as { count: number }).count

    const dirtyStmt = this.db.prepare('SELECT COUNT(*) as count FROM tabs WHERE isDirty = 1')
    const dirty = (dirtyStmt.get() as { count: number }).count

    const typeStmt = this.db.prepare('SELECT type, COUNT(*) as count FROM tabs GROUP BY type')
    const typeResults = typeStmt.all() as { type: string; count: number }[]
    const byType: Record<string, number> = {}
    typeResults.forEach((result) => {
      byType[result.type] = result.count
    })

    return {
      total,
      active,
      pinned,
      dirty,
      byType
    }
  }

  /**
   * 检查标签页是否存在
   */
  tabExists(id: string): boolean {
    const stmt = this.db.prepare('SELECT 1 FROM tabs WHERE id = ? LIMIT 1')
    return stmt.get(id) !== undefined
  }
}
