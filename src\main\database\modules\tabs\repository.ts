import type { Tab } from '../../../../shared/types'

// 数据库内部类型（SQLite 格式）
interface DbTab {
  id: string
  title: string
  active: number // 0/1
  isDirty: number // 0/1
  isPinned: number // 0/1
  icon?: string
  closable: number // 0/1
  tooltip?: string
  lastModified?: string
  metadata?: string // JSON 字符串
  path?: string
  type: string
  sort_order: number
  createdAt: string
  updatedAt: string
}
import { BaseRepository } from '../../core/base-repository'

/**
 * 标签页数据仓库
 */
export class TabRepository extends BaseRepository {
  private static instance: TabRepository

  static getInstance(): TabRepository {
    if (!TabRepository.instance) {
      TabRepository.instance = new TabRepository()
    }
    return TabRepository.instance
  }

  /**
   * 保存标签页数据（替换所有）
   */
  saveTabs(tabs: Tab[]): void {
    this.transaction(() => {
      // 清空现有标签页
      this.truncateTable('tabs')

      if (tabs.length === 0) return

      // 准备插入语句
      const stmt = this.db.prepare(`
        INSERT INTO tabs (
          id, title, active, isDirty, isPinned, icon, closable,
          tooltip, lastModified, metadata, path, type, sort_order, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      // 批量插入标签页
      for (const tab of tabs) {
        const now = new Date().toISOString()
        stmt.run(
          tab.id,
          tab.title,
          tab.active ? 1 : 0,
          tab.isDirty ? 1 : 0,
          tab.isPinned ? 1 : 0,
          tab.icon,
          tab.closable ? 1 : 0,
          tab.tooltip,
          tab.lastModified?.toISOString(),
          tab.metadata ? JSON.stringify(tab.metadata) : undefined,
          tab.path,
          tab.type,
          tab.sortOrder || 0,
          now,
          now
        )
      }
    })
  }

  /**
   * 获取所有标签页
   */
  getTabs(): Tab[] {
    const stmt = this.db.prepare('SELECT * FROM tabs ORDER BY sort_order ASC, createdAt ASC')
    const dbResults = stmt.all() as DbTab[]
    return dbResults.map(this.convertDbTabToTab)
  }

  /**
   * 根据ID获取标签页
   */
  getTabById(id: string): Tab | undefined {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE id = ?')
    const dbResult = stmt.get(id) as DbTab | undefined
    return dbResult ? this.convertDbTabToTab(dbResult) : undefined
  }

  /**
   * 插入新标签页
   */
  insertTab(tab: Tab): void {
    const stmt = this.db.prepare(`
      INSERT INTO tabs (
        id, title, active, isDirty, isPinned, icon, closable,
        tooltip, lastModified, metadata, path, type, sort_order, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const now = new Date().toISOString()
    stmt.run(
      tab.id,
      tab.title,
      tab.active ? 1 : 0,
      tab.isDirty ? 1 : 0,
      tab.isPinned ? 1 : 0,
      tab.icon,
      tab.closable ? 1 : 0,
      tab.tooltip,
      tab.lastModified?.toISOString(),
      tab.metadata ? JSON.stringify(tab.metadata) : undefined,
      tab.path,
      tab.type,
      tab.sortOrder || 0,
      now,
      now
    )
  }

  /**
   * 转换数据库格式到应用格式
   */
  private convertDbTabToTab(dbTab: DbTab): Tab {
    return {
      id: dbTab.id,
      title: dbTab.title,
      active: Boolean(dbTab.active),
      isDirty: Boolean(dbTab.isDirty),
      isPinned: Boolean(dbTab.isPinned),
      icon: dbTab.icon,
      closable: Boolean(dbTab.closable),
      tooltip: dbTab.tooltip,
      lastModified: dbTab.lastModified ? new Date(dbTab.lastModified) : undefined,
      metadata: dbTab.metadata ? JSON.parse(dbTab.metadata) : undefined,
      path: dbTab.path,
      type: dbTab.type as any,
      sortOrder: dbTab.sort_order
    }
  }

  /**
   * 更新标签页
   */
  updateTab(id: string, updates: Partial<Tab>): void {
    const fields = Object.keys(updates).filter((key) => key !== 'id')
    if (fields.length === 0) return

    // 转换应用格式到数据库格式
    const dbUpdates: Record<string, any> = {}
    for (const [key, value] of Object.entries(updates)) {
      if (key === 'id') continue

      if (typeof value === 'boolean') {
        dbUpdates[key] = value ? 1 : 0
      } else if (key === 'lastModified' && value instanceof Date) {
        dbUpdates[key] = value.toISOString()
      } else if (key === 'metadata' && typeof value === 'object') {
        dbUpdates[key] = JSON.stringify(value)
      } else if (key === 'sortOrder') {
        dbUpdates['sort_order'] = value
      } else {
        dbUpdates[key] = value
      }
    }

    const setClause = Object.keys(dbUpdates)
      .map((field) => `${field} = ?`)
      .join(', ')
    const values = Object.values(dbUpdates)

    const stmt = this.db.prepare(`
      UPDATE tabs SET ${setClause}, updatedAt = ? WHERE id = ?
    `)

    stmt.run(...values, new Date().toISOString(), id)
  }

  /**
   * 删除标签页
   */
  deleteTab(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM tabs WHERE id = ?')
    const result = stmt.run(id)
    return result.changes > 0
  }

  /**
   * 获取活跃标签页
   */
  getActiveTab(): Tab | undefined {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE active = 1 LIMIT 1')
    const dbResult = stmt.get() as DbTab | undefined
    return dbResult ? this.convertDbTabToTab(dbResult) : undefined
  }

  /**
   * 设置活跃标签页
   */
  setActiveTab(id: string): void {
    this.transaction(() => {
      // 取消所有标签页的活跃状态
      this.db.prepare('UPDATE tabs SET active = 0').run()

      // 设置指定标签页为活跃
      this.db
        .prepare('UPDATE tabs SET active = 1, updatedAt = ? WHERE id = ?')
        .run(new Date().toISOString(), id)
    })
  }

  /**
   * 获取固定标签页
   */
  getPinnedTabs(): Tab[] {
    const stmt = this.db.prepare(
      'SELECT * FROM tabs WHERE isPinned = 1 ORDER BY sort_order ASC, createdAt ASC'
    )
    const dbResults = stmt.all() as DbTab[]
    return dbResults.map(this.convertDbTabToTab)
  }

  /**
   * 获取未保存的标签页
   */
  getDirtyTabs(): Tab[] {
    const stmt = this.db.prepare(
      'SELECT * FROM tabs WHERE isDirty = 1 ORDER BY sort_order ASC, createdAt ASC'
    )
    const dbResults = stmt.all() as DbTab[]
    return dbResults.map(this.convertDbTabToTab)
  }

  /**
   * 根据类型获取标签页
   */
  getTabsByType(type: string): Tab[] {
    const stmt = this.db.prepare(
      'SELECT * FROM tabs WHERE type = ? ORDER BY sort_order ASC, createdAt ASC'
    )
    const dbResults = stmt.all(type) as DbTab[]
    return dbResults.map(this.convertDbTabToTab)
  }

  /**
   * 清理旧的标签页记录
   */
  cleanupOldTabs(daysToKeep: number = 30): number {
    return this.deleteOldRecords('tabs', 'updatedAt', daysToKeep)
  }

  /**
   * 获取标签页统计信息
   */
  getTabStats(): {
    total: number
    active: number
    pinned: number
    dirty: number
    byType: Record<string, number>
  } {
    const total = this.getTableRowCount('tabs')

    const activeStmt = this.db.prepare('SELECT COUNT(*) as count FROM tabs WHERE active = 1')
    const active = (activeStmt.get() as { count: number }).count

    const pinnedStmt = this.db.prepare('SELECT COUNT(*) as count FROM tabs WHERE isPinned = 1')
    const pinned = (pinnedStmt.get() as { count: number }).count

    const dirtyStmt = this.db.prepare('SELECT COUNT(*) as count FROM tabs WHERE isDirty = 1')
    const dirty = (dirtyStmt.get() as { count: number }).count

    const typeStmt = this.db.prepare('SELECT type, COUNT(*) as count FROM tabs GROUP BY type')
    const typeResults = typeStmt.all() as { type: string; count: number }[]
    const byType: Record<string, number> = {}
    typeResults.forEach((result) => {
      byType[result.type] = result.count
    })

    return {
      total,
      active,
      pinned,
      dirty,
      byType
    }
  }

  /**
   * 检查标签页是否存在
   */
  tabExists(id: string): boolean {
    const stmt = this.db.prepare('SELECT 1 FROM tabs WHERE id = ? LIMIT 1')
    return stmt.get(id) !== undefined
  }
}
