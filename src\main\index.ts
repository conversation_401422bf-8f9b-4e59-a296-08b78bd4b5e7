import { app, shell, BrowserWindow } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import {
  initializeDatabase,
  closeDatabase,
  cleanupDatabase,
  window as windowModule
} from './database'
import { setupDatabaseIPC } from './ipc/database'
import { setupWindowControlsIPC } from './ipc/window-controls'

// 单例模式：确保只有一个应用实例
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  // 如果获取锁失败，说明已有实例在运行，退出当前实例
  app.quit()
} else {
  // 当第二个实例尝试启动时，聚焦到第一个实例的窗口
  app.on('second-instance', () => {
    const windows = BrowserWindow.getAllWindows()
    if (windows.length > 0) {
      const mainWindow = windows[0]

      // 如果窗口被最小化，恢复它
      if (mainWindow.isMinimized()) {
        mainWindow.restore()
      }

      // 如果窗口不可见，显示它
      if (!mainWindow.isVisible()) {
        mainWindow.show()
      }

      // 聚焦窗口
      mainWindow.focus()

      // 将窗口置于最前面
      mainWindow.setAlwaysOnTop(true)
      mainWindow.setAlwaysOnTop(false)

      console.log('Second instance detected, focusing existing window')
    }
  })
}

async function createWindow(): Promise<void> {
  // 初始化数据库
  await initializeDatabase()

  // 获取保存的窗口状态
  const savedState = await windowModule.get()

  // 窗口尺寸配置
  const MIN_WIDTH = 1000 // 最小宽度
  const MIN_HEIGHT = 700 // 最小高度
  const DEFAULT_WIDTH = 1400
  const DEFAULT_HEIGHT = 900

  // 解析保存的窗口边界
  let savedBounds: { x: number; y: number; width: number; height: number } | null = null
  if (savedState?.bounds) {
    // bounds 现在已经是对象类型，不需要 JSON.parse
    savedBounds = {
      x: savedState.bounds.x || 0,
      y: savedState.bounds.y || 0,
      width: Math.max(savedState.bounds.width || DEFAULT_WIDTH, MIN_WIDTH),
      height: Math.max(savedState.bounds.height || DEFAULT_HEIGHT, MIN_HEIGHT)
    }
  }

  // 创建浏览器窗口
  const mainWindow = new BrowserWindow({
    width: savedBounds?.width || DEFAULT_WIDTH,
    height: savedBounds?.height || DEFAULT_HEIGHT,
    minWidth: MIN_WIDTH,
    minHeight: MIN_HEIGHT,
    x: savedBounds?.x,
    y: savedBounds?.y,
    show: false,
    resizable: true, // 明确启用窗口大小调整
    frame: false, // 隐藏原生标题栏
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 恢复窗口状态
  if (savedState) {
    if (savedState.isMaximized) {
      mainWindow.maximize()
    }
  }

  // 设置IPC处理程序
  setupDatabaseIPC()
  setupWindowControlsIPC(mainWindow)

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 窗口状态变化监听
  const saveWindowState = async () => {
    try {
      const bounds = mainWindow.getBounds()
      await windowModule.save({
        isMaximized: mainWindow.isMaximized(),
        isMinimized: mainWindow.isMinimized(),
        isFullscreen: mainWindow.isFullScreen(),
        isFocused: mainWindow.isFocused(),
        bounds: bounds
      })
    } catch (error) {
      console.error('Failed to save window state:', error)
    }
  }

  // 监听窗口状态变化
  mainWindow.on('maximize', () => {
    mainWindow.webContents.send('window:state-changed', { isMaximized: true })
    saveWindowState()
  })

  mainWindow.on('unmaximize', () => {
    mainWindow.webContents.send('window:state-changed', { isMaximized: false })
    saveWindowState()
  })

  mainWindow.on('focus', () => {
    mainWindow.webContents.send('window:state-changed', { isFocused: true })
    saveWindowState()
  })

  mainWindow.on('blur', () => {
    mainWindow.webContents.send('window:state-changed', { isFocused: false })
    saveWindowState()
  })

  mainWindow.on('resize', () => {
    // 确保窗口大小不小于最小限制
    const bounds = mainWindow.getBounds()
    if (bounds.width < MIN_WIDTH || bounds.height < MIN_HEIGHT) {
      mainWindow.setBounds({
        ...bounds,
        width: Math.max(bounds.width, MIN_WIDTH),
        height: Math.max(bounds.height, MIN_HEIGHT)
      })
    }
    saveWindowState()
  })

  mainWindow.on('move', () => {
    saveWindowState()
  })

  // 基于electron-vite cli的渲染器热更新
  // 开发环境下加载远程URL，生产环境下加载本地HTML文件
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
// 某些API只能在此事件发生后使用
app.whenReady().then(async () => {
  // 为Windows设置应用程序用户模型ID
  electronApp.setAppUserModelId('com.aikairo.tbaw')

  // 在开发环境中通过F12默认打开或关闭开发者工具
  // 在生产环境中忽略CommandOrControl + R
  // 详见 https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // 只有获得锁的实例才创建窗口
  if (gotTheLock) {
    await createWindow()
  }

  app.on('activate', async function () {
    // 在macOS上，当点击dock图标且没有其他窗口打开时，
    // 通常会在应用程序中重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0 && gotTheLock) {
      await createWindow()
    }
  })
})

// 当所有窗口都被关闭时退出应用，除了在macOS上
// 在macOS上，应用及其菜单栏通常会保持活动状态，
// 直到用户使用Cmd + Q明确退出
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 应用退出前的清理工作
app.on('before-quit', async () => {
  try {
    await cleanupDatabase()
    await closeDatabase()
  } catch (error) {
    console.error('Application cleanup failed:', error)
  }
})

// 在这个文件中，你可以包含应用程序特定主进程的其余代码。
// 你也可以将它们放在单独的文件中，并在这里引入它们。
