import type { DatabaseWindowState } from '../../../shared/types'
import { WindowRepository } from '../repositories/window-repository'

export class WindowService {
  private windowRepository: WindowRepository

  constructor(windowRepository: WindowRepository) {
    this.windowRepository = windowRepository
  }

  /**
   * 保存窗口状态
   */
  async saveWindowState(state: Omit<DatabaseWindowState, 'id' | 'updatedAt'>): Promise<void> {
    // 验证窗口状态数据
    this.validateWindowState(state)

    await this.windowRepository.saveWindowState(state)
  }

  /**
   * 获取窗口状态
   */
  async getWindowState(): Promise<DatabaseWindowState | undefined> {
    return await this.windowRepository.getWindowState()
  }

  /**
   * 更新窗口边界
   */
  async updateWindowBounds(bounds: { x: number; y: number; width: number; height: number }): Promise<void> {
    // 验证边界数据
    this.validateBounds(bounds)

    const boundsString = JSON.stringify(bounds)
    await this.windowRepository.updateWindowBounds(boundsString)
  }

  /**
   * 更新窗口最大化状态
   */
  async updateMaximizedState(isMaximized: boolean): Promise<void> {
    await this.windowRepository.updateMaximizedState(isMaximized)
  }

  /**
   * 更新窗口最小化状态
   */
  async updateMinimizedState(isMinimized: boolean): Promise<void> {
    await this.windowRepository.updateMinimizedState(isMinimized)
  }

  /**
   * 更新窗口全屏状态
   */
  async updateFullscreenState(isFullscreen: boolean): Promise<void> {
    await this.windowRepository.updateFullscreenState(isFullscreen)
  }

  /**
   * 更新窗口焦点状态
   */
  async updateFocusState(isFocused: boolean): Promise<void> {
    await this.windowRepository.updateFocusState(isFocused)
  }

  /**
   * 清除窗口状态
   */
  async clearWindowState(): Promise<void> {
    await this.windowRepository.clearWindowState()
  }

  /**
   * 获取默认窗口状态
   */
  getDefaultWindowState(): Omit<DatabaseWindowState, 'id' | 'updatedAt'> {
    return {
      isMaximized: false,
      isMinimized: false,
      isFullscreen: false,
      isFocused: true,
      bounds: JSON.stringify({
        x: 100,
        y: 100,
        width: 1200,
        height: 800
      })
    }
  }

  /**
   * 初始化窗口状态
   */
  async initializeWindowState(): Promise<void> {
    const existingState = await this.getWindowState()
    
    if (!existingState) {
      const defaultState = this.getDefaultWindowState()
      await this.saveWindowState(defaultState)
    }
  }

  /**
   * 解析窗口边界
   */
  parseWindowBounds(boundsString: string): { x: number; y: number; width: number; height: number } | null {
    try {
      const bounds = JSON.parse(boundsString)
      this.validateBounds(bounds)
      return bounds
    } catch (error) {
      console.error('Failed to parse window bounds:', error)
      return null
    }
  }

  /**
   * 验证窗口状态数据
   */
  private validateWindowState(state: Omit<DatabaseWindowState, 'id' | 'updatedAt'>): void {
    if (typeof state.isMaximized !== 'boolean') {
      throw new Error('isMaximized must be a boolean')
    }
    if (typeof state.isMinimized !== 'boolean') {
      throw new Error('isMinimized must be a boolean')
    }
    if (typeof state.isFullscreen !== 'boolean') {
      throw new Error('isFullscreen must be a boolean')
    }
    if (typeof state.isFocused !== 'boolean') {
      throw new Error('isFocused must be a boolean')
    }
    if (typeof state.bounds !== 'string') {
      throw new Error('bounds must be a string')
    }

    // 验证边界数据
    try {
      const bounds = JSON.parse(state.bounds)
      this.validateBounds(bounds)
    } catch (error) {
      throw new Error('Invalid bounds format')
    }
  }

  /**
   * 验证边界数据
   */
  private validateBounds(bounds: any): void {
    if (!bounds || typeof bounds !== 'object') {
      throw new Error('Bounds must be an object')
    }

    const { x, y, width, height } = bounds

    if (typeof x !== 'number' || !isFinite(x)) {
      throw new Error('Bounds x must be a finite number')
    }
    if (typeof y !== 'number' || !isFinite(y)) {
      throw new Error('Bounds y must be a finite number')
    }
    if (typeof width !== 'number' || !isFinite(width) || width <= 0) {
      throw new Error('Bounds width must be a positive finite number')
    }
    if (typeof height !== 'number' || !isFinite(height) || height <= 0) {
      throw new Error('Bounds height must be a positive finite number')
    }

    // 检查合理的窗口大小范围
    if (width < 200 || width > 10000) {
      throw new Error('Window width must be between 200 and 10000 pixels')
    }
    if (height < 150 || height > 10000) {
      throw new Error('Window height must be between 150 and 10000 pixels')
    }
  }
}
