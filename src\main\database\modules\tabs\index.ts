/**
 * 标签页模块
 * 提供标签页相关的所有功能
 */

import { TabService } from './service'

// 获取服务实例
const tabService = TabService.getInstance()

// 导出便捷方法
const tabs = {
  // 基础操作
  save: tabService.saveTabs.bind(tabService),
  getAll: tabService.getTabs.bind(tabService),
  getById: tabService.getTabById.bind(tabService),
  create: tabService.createTab.bind(tabService),
  update: tabService.updateTab.bind(tabService),
  delete: tabService.deleteTab.bind(tabService),

  // 状态管理
  getActive: tabService.getActiveTab.bind(tabService),
  setActive: tabService.setActiveTab.bind(tabService),
  getPinned: tabService.getPinnedTabs.bind(tabService),
  getDirty: tabService.getDirtyTabs.bind(tabService),
  getByType: tabService.getTabsByType.bind(tabService),

  // 维护操作
  cleanup: tabService.cleanupOldTabs.bind(tabService),
  getStats: tabService.getTabStats.bind(tabService)
}

// 默认导出便捷对象
export default tabs
