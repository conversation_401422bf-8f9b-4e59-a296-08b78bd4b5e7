import type { WindowState } from '../../../../shared/types'
import { WindowRepository } from './repository'

/**
 * 窗口状态业务服务
 */
export class WindowService {
  private static instance: WindowService
  private repository: WindowRepository

  private constructor() {
    this.repository = WindowRepository.getInstance()
  }

  static getInstance(): WindowService {
    if (!WindowService.instance) {
      WindowService.instance = new WindowService()
    }
    return WindowService.instance
  }

  /**
   * 保存窗口状态
   */
  async saveWindowState(state: Omit<WindowState, 'id' | 'updatedAt'>): Promise<void> {
    // 验证窗口状态数据
    this.validateWindowState(state)

    this.repository.saveWindowState(state)
  }

  /**
   * 获取窗口状态
   */
  async getWindowState(): Promise<WindowState | undefined> {
    return this.repository.getWindowState()
  }

  /**
   * 更新窗口边界
   */
  async updateWindowBounds(bounds: {
    x: number
    y: number
    width: number
    height: number
  }): Promise<void> {
    // 验证边界数据
    this.validateBounds(bounds)

    const boundsString = JSON.stringify(bounds)
    this.repository.updateWindowBounds(boundsString)
  }

  /**
   * 更新窗口最大化状态
   */
  async updateMaximizedState(isMaximized: boolean): Promise<void> {
    this.repository.updateMaximizedState(isMaximized ? 1 : 0)
  }

  /**
   * 更新窗口最小化状态
   */
  async updateMinimizedState(isMinimized: boolean): Promise<void> {
    this.repository.updateMinimizedState(isMinimized ? 1 : 0)
  }

  /**
   * 更新窗口全屏状态
   */
  async updateFullscreenState(isFullscreen: boolean): Promise<void> {
    this.repository.updateFullscreenState(isFullscreen ? 1 : 0)
  }

  /**
   * 更新窗口焦点状态
   */
  async updateFocusState(isFocused: boolean): Promise<void> {
    this.repository.updateFocusState(isFocused ? 1 : 0)
  }

  /**
   * 清除窗口状态
   */
  async clearWindowState(): Promise<void> {
    this.repository.clearWindowState()
  }

  /**
   * 检查窗口状态是否存在
   */
  async windowStateExists(): Promise<boolean> {
    return this.repository.windowStateExists()
  }

  /**
   * 获取默认窗口状态
   */
  getDefaultWindowState(): Omit<WindowState, 'id' | 'updatedAt'> {
    return {
      isMaximized: false,
      isMinimized: false,
      isFullscreen: false,
      isFocused: true,
      bounds: {
        x: 100,
        y: 100,
        width: 1200,
        height: 800
      }
    }
  }

  /**
   * 初始化窗口状态
   */
  async initializeWindowState(): Promise<void> {
    this.repository.initializeDefaultWindowState()
  }

  /**
   * 解析窗口边界
   */
  parseWindowBounds(
    boundsString: string
  ): { x: number; y: number; width: number; height: number } | null {
    try {
      const bounds = JSON.parse(boundsString)
      this.validateBounds(bounds)
      return bounds
    } catch (error) {
      console.error('Failed to parse window bounds:', error)
      return null
    }
  }

  /**
   * 获取窗口状态的更新时间
   */
  async getWindowStateUpdatedAt(): Promise<string | undefined> {
    return this.repository.getWindowStateUpdatedAt()
  }

  /**
   * 批量更新窗口状态
   */
  async updateWindowStateFields(updates: {
    isMaximized?: boolean
    isMinimized?: boolean
    isFullscreen?: boolean
    isFocused?: boolean
    bounds?: { x: number; y: number; width: number; height: number }
  }): Promise<void> {
    const dbUpdates: any = {}

    if (updates.isMaximized !== undefined) {
      dbUpdates.isMaximized = updates.isMaximized ? 1 : 0
    }
    if (updates.isMinimized !== undefined) {
      dbUpdates.isMinimized = updates.isMinimized ? 1 : 0
    }
    if (updates.isFullscreen !== undefined) {
      dbUpdates.isFullscreen = updates.isFullscreen ? 1 : 0
    }
    if (updates.isFocused !== undefined) {
      dbUpdates.isFocused = updates.isFocused ? 1 : 0
    }
    if (updates.bounds !== undefined) {
      this.validateBounds(updates.bounds)
      dbUpdates.bounds = JSON.stringify(updates.bounds)
    }

    if (Object.keys(dbUpdates).length > 0) {
      this.repository.updateWindowStateFields(dbUpdates)
    }
  }

  /**
   * 验证窗口状态数据
   */
  private validateWindowState(state: Omit<WindowState, 'id' | 'updatedAt'>): void {
    if (typeof state.isMaximized !== 'boolean') {
      throw new Error('isMaximized must be a boolean')
    }
    if (typeof state.isMinimized !== 'boolean') {
      throw new Error('isMinimized must be a boolean')
    }
    if (typeof state.isFullscreen !== 'boolean') {
      throw new Error('isFullscreen must be a boolean')
    }
    if (typeof state.isFocused !== 'boolean') {
      throw new Error('isFocused must be a boolean')
    }

    // 验证边界数据
    if (state.bounds) {
      this.validateBounds(state.bounds)
    }
  }

  /**
   * 验证边界数据
   */
  private validateBounds(bounds: any): void {
    if (!bounds || typeof bounds !== 'object') {
      throw new Error('Bounds must be an object')
    }

    const { x, y, width, height } = bounds

    if (typeof x !== 'number' || !isFinite(x)) {
      throw new Error('Bounds x must be a finite number')
    }
    if (typeof y !== 'number' || !isFinite(y)) {
      throw new Error('Bounds y must be a finite number')
    }
    if (typeof width !== 'number' || !isFinite(width) || width <= 0) {
      throw new Error('Bounds width must be a positive finite number')
    }
    if (typeof height !== 'number' || !isFinite(height) || height <= 0) {
      throw new Error('Bounds height must be a positive finite number')
    }

    // 检查合理的窗口大小范围
    if (width < 200 || width > 10000) {
      throw new Error('Window width must be between 200 and 10000 pixels')
    }
    if (height < 150 || height > 10000) {
      throw new Error('Window height must be between 150 and 10000 pixels')
    }
  }
}
