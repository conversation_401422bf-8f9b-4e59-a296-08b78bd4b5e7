/**
 * 类型验证和转换工具
 * 用于验证和转换不同层级之间的数据类型
 */

import {
  TabSchema,
  DatabaseTabSchema,
  ServiceTabSchema,
  WindowStateSchema,
  DatabaseWindowStateSchema,
  ServiceWindowStateSchema,
  AppSettingsSchema,
  TitlebarConfigSchema,
  RecentFileSchema,
  DatabaseRecentFileSchema,
  ApiResponseSchema,
  type Tab,
  type DatabaseTab,
  type ServiceTab,
  type WindowState,
  type DatabaseWindowState,
  type ServiceWindowState,
  type AppSettings,
  type TitlebarConfig,
  type RecentFile,
  type DatabaseRecentFile,
  createApiResponse
} from './types'

// =============================================================================
// 数据转换工具函数
// =============================================================================

/**
 * 将业务层 Tab 转换为服务层 ServiceTab
 */
export function tabToServiceTab(tab: Tab): ServiceTab {
  return {
    id: tab.id,
    title: tab.title,
    active: tab.active,
    isDirty: tab.isDirty,
    isPinned: tab.isPinned,
    icon: tab.icon,
    closable: tab.closable,
    tooltip: tab.tooltip,
    lastModified: tab.lastModified?.toISOString(),
    metadata: tab.metadata ? JSON.stringify(tab.metadata) : undefined,
    path: tab.path,
    type: tab.type,
    sort_order: tab.sortOrder,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

/**
 * 将服务层 ServiceTab 转换为数据库层 DatabaseTab
 */
export function serviceTabToDatabaseTab(serviceTab: ServiceTab): DatabaseTab {
  return {
    id: serviceTab.id,
    title: serviceTab.title,
    active: serviceTab.active ? 1 : 0,
    isDirty: serviceTab.isDirty ? 1 : 0,
    isPinned: serviceTab.isPinned ? 1 : 0,
    icon: serviceTab.icon,
    closable: serviceTab.closable ? 1 : 0,
    tooltip: serviceTab.tooltip,
    lastModified: serviceTab.lastModified,
    metadata: serviceTab.metadata,
    path: serviceTab.path,
    type: serviceTab.type,
    sort_order: serviceTab.sort_order,
    createdAt: serviceTab.createdAt,
    updatedAt: serviceTab.updatedAt
  }
}

/**
 * 将数据库层 DatabaseTab 转换为服务层 ServiceTab
 */
export function databaseTabToServiceTab(dbTab: DatabaseTab): ServiceTab {
  return {
    id: dbTab.id,
    title: dbTab.title,
    active: Boolean(dbTab.active),
    isDirty: Boolean(dbTab.isDirty),
    isPinned: Boolean(dbTab.isPinned),
    icon: dbTab.icon,
    closable: Boolean(dbTab.closable),
    tooltip: dbTab.tooltip,
    lastModified: dbTab.lastModified,
    metadata: dbTab.metadata,
    path: dbTab.path,
    type: dbTab.type,
    sort_order: dbTab.sort_order,
    createdAt: dbTab.createdAt,
    updatedAt: dbTab.updatedAt
  }
}

/**
 * 将服务层 ServiceTab 转换为业务层 Tab
 */
export function serviceTabToTab(serviceTab: ServiceTab): Tab {
  return {
    id: serviceTab.id,
    title: serviceTab.title,
    active: serviceTab.active,
    isDirty: serviceTab.isDirty,
    isPinned: serviceTab.isPinned,
    icon: serviceTab.icon,
    closable: serviceTab.closable,
    tooltip: serviceTab.tooltip,
    lastModified: serviceTab.lastModified ? new Date(serviceTab.lastModified) : undefined,
    metadata: serviceTab.metadata ? JSON.parse(serviceTab.metadata) : undefined,
    path: serviceTab.path,
    type: serviceTab.type as any, // 需要类型断言，因为服务层使用 string，业务层使用枚举
    sortOrder: serviceTab.sort_order
  }
}

// =============================================================================
// 数据验证工具函数
// =============================================================================

/**
 * 验证业务层 Tab 数据
 */
export function validateTab(data: unknown): Tab {
  return TabSchema.parse(data)
}

/**
 * 验证数据库层 DatabaseTab 数据
 */
export function validateDatabaseTab(data: unknown): DatabaseTab {
  return DatabaseTabSchema.parse(data)
}

/**
 * 验证服务层 ServiceTab 数据
 */
export function validateServiceTab(data: unknown): ServiceTab {
  return ServiceTabSchema.parse(data)
}

/**
 * 验证窗口状态数据
 */
export function validateWindowState(data: unknown): WindowState {
  return WindowStateSchema.parse(data)
}

/**
 * 验证应用设置数据
 */
export function validateAppSettings(data: unknown): AppSettings {
  return AppSettingsSchema.parse(data)
}

/**
 * 验证标题栏配置数据
 */
export function validateTitlebarConfig(data: unknown): TitlebarConfig {
  return TitlebarConfigSchema.parse(data)
}

// =============================================================================
// 批量转换工具函数
// =============================================================================

/**
 * 批量转换业务层 Tab 数组为服务层 ServiceTab 数组
 */
export function tabsToServiceTabs(tabs: Tab[]): ServiceTab[] {
  return tabs.map(tabToServiceTab)
}

/**
 * 批量转换服务层 ServiceTab 数组为数据库层 DatabaseTab 数组
 */
export function serviceTabsToDatabaseTabs(serviceTabs: ServiceTab[]): DatabaseTab[] {
  return serviceTabs.map(serviceTabToDatabaseTab)
}

/**
 * 批量转换数据库层 DatabaseTab 数组为服务层 ServiceTab 数组
 */
export function databaseTabsToServiceTabs(dbTabs: DatabaseTab[]): ServiceTab[] {
  return dbTabs.map(databaseTabToServiceTab)
}

/**
 * 批量转换服务层 ServiceTab 数组为业务层 Tab 数组
 */
export function serviceTabsToTabs(serviceTabs: ServiceTab[]): Tab[] {
  return serviceTabs.map(serviceTabToTab)
}

// =============================================================================
// 类型检查工具函数
// =============================================================================

/**
 * 检查数据是否为有效的 Tab 类型
 */
export function isValidTab(data: unknown): data is Tab {
  try {
    TabSchema.parse(data)
    return true
  } catch {
    return false
  }
}

/**
 * 检查数据是否为有效的 DatabaseTab 类型
 */
export function isValidDatabaseTab(data: unknown): data is DatabaseTab {
  try {
    DatabaseTabSchema.parse(data)
    return true
  } catch {
    return false
  }
}

/**
 * 检查数据是否为有效的 ServiceTab 类型
 */
export function isValidServiceTab(data: unknown): data is ServiceTab {
  try {
    ServiceTabSchema.parse(data)
    return true
  } catch {
    return false
  }
}

/**
 * 安全的 API 响应创建函数（带类型验证）
 */
export function createSafeApiResponse<T = any>(
  success: boolean,
  data?: T,
  error?: string
) {
  const response = createApiResponse(success, data, error)
  return ApiResponseSchema.parse(response)
}
