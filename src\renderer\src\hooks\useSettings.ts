import { useState, useEffect, useCallback } from 'react'
import type { AppSettings } from '@/types'
import { AppSettingsSchema } from '@/types'

export function useSettings() {
  const [settings, setSettings] = useState<AppSettings>(() => 
    AppSettingsSchema.parse({})
  )
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 加载设置
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const response = await window.api.database.getAllSettings()
      if (response.success && response.data) {
        // 使用schema验证和填充默认值
        const validatedSettings = AppSettingsSchema.parse(response.data)
        setSettings(validatedSettings)
      } else {
        throw new Error(response.error || 'Failed to load settings')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Failed to load settings:', err)
      
      // 使用默认设置
      setSettings(AppSettingsSchema.parse({}))
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 保存单个设置
  const saveSetting = useCallback(async <K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ): Promise<boolean> => {
    try {
      const response = await window.api.database.saveSetting(key, value)
      if (response.success) {
        setSettings(prev => ({ ...prev, [key]: value }))
        return true
      } else {
        throw new Error(response.error || 'Failed to save setting')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error(`Failed to save setting ${String(key)}:`, err)
      return false
    }
  }, [])

  // 批量保存设置
  const saveSettings = useCallback(async (newSettings: Partial<AppSettings>): Promise<boolean> => {
    try {
      // 验证设置
      const mergedSettings = { ...settings, ...newSettings }
      const validatedSettings = AppSettingsSchema.parse(mergedSettings)
      
      // 保存每个设置
      const savePromises = Object.entries(newSettings).map(([key, value]) =>
        window.api.database.saveSetting(key, value)
      )
      
      const responses = await Promise.all(savePromises)
      const allSuccessful = responses.every(response => response.success)
      
      if (allSuccessful) {
        setSettings(validatedSettings)
        return true
      } else {
        const failedResponses = responses.filter(response => !response.success)
        throw new Error(`Failed to save some settings: ${failedResponses.map(r => r.error).join(', ')}`)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Failed to save settings:', err)
      return false
    }
  }, [settings])

  // 重置设置到默认值
  const resetSettings = useCallback(async (): Promise<boolean> => {
    try {
      const defaultSettings = AppSettingsSchema.parse({})
      return await saveSettings(defaultSettings)
    } catch (err) {
      console.error('Failed to reset settings:', err)
      return false
    }
  }, [saveSettings])

  // 获取单个设置
  const getSetting = useCallback(<K extends keyof AppSettings>(
    key: K
  ): AppSettings[K] => {
    return settings[key]
  }, [settings])

  // 初始化时加载设置
  useEffect(() => {
    loadSettings()
  }, [loadSettings])

  return {
    settings,
    isLoading,
    error,
    loadSettings,
    saveSetting,
    saveSettings,
    resetSettings,
    getSetting
  }
}
