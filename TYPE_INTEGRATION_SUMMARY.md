# 类型定义整合总结

## 概述

本次整合工作将项目中重复的类型定义统一使用 zod 进行管理，去除了重复定义，建立了简洁统一的类型架构。

## 整合前的问题

1. **重复的类型定义**：同一个概念在不同文件中有多个 interface 定义
2. **过度复杂化**：为同一概念创建了多个层级的类型定义
3. **缺乏验证**：没有运行时类型验证机制
4. **维护困难**：类型定义分散，修改时需要同步多个地方

## 整合后的架构

### 统一类型架构

采用**单一类型定义**原则，每个概念只有一个主要类型定义：

- **用途**：整个应用程序统一使用
- **特点**：使用 TypeScript 原生类型，如 `boolean`、`Date`、`Record<string, any>`
- **示例**：`Tab`、`WindowState`、`AppSettings`、`RecentFile`

### 数据库层处理

数据库层在内部处理数据格式转换：

- 使用内部接口（如 `DbTab`、`DbWindowState`）处理 SQLite 格式
- 在 repository 层自动转换数据格式
- 对外暴露统一的应用类型

### 核心 Zod Schema

```typescript
// 统一的标签页 Schema
export const TabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.boolean().default(false).describe('是否为当前活跃标签页'),
  isDirty: z.boolean().default(false).describe('是否有未保存的更改'),
  isPinned: z.boolean().default(false).describe('是否为固定标签页'),
  sortOrder: z.number().default(0).describe('标签页排序顺序')
})

// 统一的窗口状态 Schema
export const WindowStateSchema = z.object({
  id: z.number().default(1).describe('窗口ID'),
  isMaximized: z.boolean().default(false).describe('是否最大化'),
  isMinimized: z.boolean().default(false).describe('是否最小化'),
  bounds: BoundsSchema.optional().describe('窗口边界信息')
})
```

## 主要改进

### 1. 去除重复定义

- 删除了 `DatabaseTab`、`ServiceTab` 等重复类型
- 统一使用 `Tab` 类型贯穿整个应用
- 数据库层内部处理格式转换

### 2. 完整的字段注释

每个字段都有详细的中文注释，说明用途和格式：

```typescript
active: z.boolean().default(false).describe('是否为当前活跃标签页'),
sortOrder: z.number().default(0).describe('标签页排序顺序'),
```

### 3. 内部数据转换

在数据库 repository 层处理格式转换：

- 应用层使用 `boolean`，数据库存储为 `0/1`
- 应用层使用 `Date`，数据库存储为 ISO 字符串
- 应用层使用 `object`，数据库存储为 JSON 字符串

### 4. 简化的架构

- **应用层**：直接使用 zod 推断的类型
- **数据库层**：内部接口处理 SQLite 格式
- **自动转换**：repository 层负责格式转换

## 文件变更清单

### 修改的文件

1. `src/shared/types.ts` - 简化为统一的 zod schema 定义
2. `src/main/ipc/database.ts` - 简化类型转换逻辑，添加本地 createApiResponse 函数
3. `src/main/ipc/window-controls.ts` - 添加本地 createApiResponse 函数
4. `src/main/database/index.ts` - 清理导出，只保留必要的模块
5. `src/main/database/modules/tabs/index.ts` - 简化导出，只保留便捷方法
6. `src/main/database/modules/settings/index.ts` - 简化导出，只保留核心功能
7. `src/main/database/modules/window/index.ts` - 简化导出，只保留基础操作
8. `src/main/database/modules/tabs/repository.ts` - 内部处理数据转换
9. `src/main/database/modules/tabs/service.ts` - 使用统一类型
10. `src/main/database/modules/settings/repository.ts` - 使用内部接口
11. `src/main/database/modules/window/repository.ts` - 内部处理数据转换
12. `src/main/database/modules/window/service.ts` - 使用统一类型
13. `src/main/index.ts` - 修复窗口状态类型问题

### 删除的文件

1. `src/shared/type-validation.ts` - 不再需要复杂的转换工具

## 使用示例

### 直接使用统一类型

```typescript
import { Tab, WindowState, TabSchema } from '@/shared/types'

// 直接使用类型
const tab: Tab = {
  id: 'tab-1',
  title: '首页',
  active: true,
  isDirty: false,
  isPinned: false,
  closable: false,
  type: 'home',
  sortOrder: 0
}

// 使用 zod 验证
const validatedTab = TabSchema.parse(unknownData)
```

### 数据库层自动转换

```typescript
// Repository 层自动处理转换
const tabRepository = TabRepository.getInstance()

// 保存时自动转换为数据库格式
await tabRepository.insertTab(tab)

// 获取时自动转换为应用格式
const tabs = await tabRepository.getTabs()
```

## 优势

1. **简化架构**：去除了不必要的类型层级
2. **类型安全**：zod 提供编译时和运行时验证
3. **易于维护**：单一类型定义，修改一处即可
4. **自动转换**：数据库层内部处理格式转换
5. **文档化**：每个字段都有详细注释
6. **扩展性**：新增字段只需在 schema 中定义

## 注意事项

1. 数据库迁移已整合到第一个版本，包含所有必要的表结构
2. 所有字段都有详细的中文注释说明用途
3. 数据库层内部处理格式转换，对外透明
4. 保持了向后兼容性，现有代码无需大幅修改

## 后续建议

1. 在新增功能时，优先使用 zod schema 定义类型
2. 数据库层的格式转换在 repository 内部处理
3. 在 API 边界处使用 zod 验证确保数据正确性
4. 定期检查和更新类型定义，保持与业务需求同步
