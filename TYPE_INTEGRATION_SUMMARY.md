# 类型定义整合总结

## 概述

本次整合工作将项目中重复的类型定义统一使用 zod 进行管理，去除了重复定义，建立了清晰的类型层级结构。

## 整合前的问题

1. **重复的类型定义**：同一个概念在不同文件中有多个 interface 定义
2. **类型不一致**：数据库层、服务层、业务层使用不同的类型定义
3. **缺乏验证**：没有运行时类型验证机制
4. **字段映射混乱**：数据库字段名与 TypeScript 属性名不一致

## 整合后的架构

### 三层类型架构

#### 1. 业务层类型 (Business Layer)
- **用途**：前端组件和业务逻辑使用
- **特点**：使用 TypeScript 原生类型，如 `boolean`、`Date`、`Record<string, any>`
- **示例**：`Tab`、`WindowState`、`AppSettings`

#### 2. 服务层类型 (Service Layer)  
- **用途**：业务服务和数据转换使用
- **特点**：使用序列化友好的类型，如 `boolean`、`string`（ISO 日期）、`string`（JSON）
- **示例**：`ServiceTab`、`ServiceWindowState`

#### 3. 数据库层类型 (Database Layer)
- **用途**：数据库操作和存储使用
- **特点**：使用 SQLite 兼容的类型，如 `number`（0/1 表示布尔值）、`string`
- **示例**：`DatabaseTab`、`DatabaseWindowState`、`DatabaseSettings`

### 核心 Zod Schema

```typescript
// 业务层 Schema
export const TabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.boolean().default(false).describe('是否为当前活跃标签页'),
  // ... 其他字段
})

// 数据库层 Schema  
export const DatabaseTabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.number().min(0).max(1).describe('是否为当前活跃标签页 (0=否, 1=是)'),
  // ... 其他字段
})

// 服务层 Schema
export const ServiceTabSchema = z.object({
  id: z.string().min(1).describe('标签页唯一标识符'),
  title: z.string().min(1).describe('标签页显示标题'),
  active: z.boolean().describe('是否为当前活跃标签页'),
  // ... 其他字段
})
```

## 主要改进

### 1. 统一的类型定义
- 所有类型定义集中在 `src/shared/types.ts`
- 使用 zod schema 作为单一数据源
- 通过 `z.infer<typeof Schema>` 生成 TypeScript 类型

### 2. 完整的字段注释
每个字段都有详细的中文注释，说明用途和格式：
```typescript
active: z.boolean().default(false).describe('是否为当前活跃标签页'),
sort_order: z.number().default(0).describe('标签页排序顺序'),
```

### 3. 数据转换工具
创建了 `src/shared/type-validation.ts` 提供：
- 类型验证函数：`validateTab()`, `validateDatabaseTab()` 等
- 数据转换函数：`tabToServiceTab()`, `serviceTabToDatabaseTab()` 等
- 批量转换函数：`tabsToServiceTabs()` 等
- 类型检查函数：`isValidTab()` 等

### 4. 字段映射规范化
解决了数据库字段名与 TypeScript 属性名的映射问题：
- 数据库：`sort_order` (snake_case)
- TypeScript：`sortOrder` (camelCase)
- 在转换函数中正确映射

## 文件变更清单

### 修改的文件
1. `src/shared/types.ts` - 重构为统一的 zod schema 定义
2. `src/main/ipc/database.ts` - 更新类型转换逻辑
3. `src/main/database/modules/tabs/repository.ts` - 更新 SQL 查询包含 sort_order
4. `src/main/database/modules/tabs/service.ts` - 更新类型导入
5. `src/main/database/modules/settings/repository.ts` - 更新类型导入
6. `src/main/database/modules/window/repository.ts` - 更新类型导入
7. `src/main/database/modules/window/service.ts` - 更新类型导入

### 新增的文件
1. `src/shared/type-validation.ts` - 类型验证和转换工具

## 使用示例

### 验证数据
```typescript
import { validateTab, validateDatabaseTab } from '@/shared/type-validation'

// 验证业务层数据
const tab = validateTab(unknownData)

// 验证数据库层数据  
const dbTab = validateDatabaseTab(dbResult)
```

### 转换数据
```typescript
import { tabToServiceTab, databaseTabToServiceTab } from '@/shared/type-validation'

// 业务层 -> 服务层
const serviceTab = tabToServiceTab(tab)

// 数据库层 -> 服务层
const serviceTab = databaseTabToServiceTab(dbTab)
```

### 类型检查
```typescript
import { isValidTab, isValidDatabaseTab } from '@/shared/type-validation'

if (isValidTab(data)) {
  // data 现在是 Tab 类型
  console.log(data.sortOrder)
}
```

## 优势

1. **类型安全**：编译时和运行时双重类型检查
2. **代码复用**：统一的类型定义，避免重复
3. **易于维护**：单一数据源，修改一处即可
4. **自动验证**：zod 提供运行时验证和错误提示
5. **文档化**：每个字段都有详细注释
6. **扩展性**：新增字段只需在 schema 中定义

## 注意事项

1. 数据库迁移已整合到第一个版本，包含所有必要的表结构
2. 所有字段都有详细的中文注释说明用途
3. 类型转换函数处理了不同层级之间的数据格式差异
4. 保持了向后兼容性，现有代码无需大幅修改

## 后续建议

1. 在新增功能时，优先使用 zod schema 定义类型
2. 使用提供的转换工具函数进行数据转换
3. 在 API 边界处使用验证函数确保数据正确性
4. 定期检查和更新类型定义，保持与业务需求同步
