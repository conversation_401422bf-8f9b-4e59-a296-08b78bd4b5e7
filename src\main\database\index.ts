/**
 * 数据库模块主入口
 * 提供模块化的数据库功能
 */

import CoreDatabaseManager from './core/database-manager'

// 导入所有模块
import tabs from './modules/tabs'
import settings from './modules/settings'
import window from './modules/window'

// 导出模块
export { tabs, settings, window }

// 导出服务和仓库实例（用于高级操作）
export { tabService, tabRepository } from './modules/tabs'
export { settingsService, settingsRepository } from './modules/settings'
export { windowService, windowRepository } from './modules/window'

// 导出核心管理器
export { CoreDatabaseManager }

/**
 * 初始化数据库
 * 必须在使用任何数据库功能之前调用
 */
export async function initializeDatabase(): Promise<void> {
  const dbManager = CoreDatabaseManager.getInstance()
  await dbManager.initialize()

  // 初始化各模块的默认数据
  await settings.initDefaults()
  await window.initialize()
}

/**
 * 关闭数据库连接
 */
export async function closeDatabase(): Promise<void> {
  const dbManager = CoreDatabaseManager.getInstance()
  await dbManager.close()
}

/**
 * 获取数据库统计信息
 */
export function getDatabaseStats() {
  const dbManager = CoreDatabaseManager.getInstance()
  return dbManager.getStats()
}

/**
 * 优化数据库
 */
export async function optimizeDatabase(): Promise<void> {
  const dbManager = CoreDatabaseManager.getInstance()
  await dbManager.optimize()
}

/**
 * 清理数据库
 */
export async function cleanupDatabase(): Promise<void> {
  // 清理旧的标签页记录
  await tabs.cleanup(30)

  // 清理空的设置
  await settings.cleanup()

  // 优化数据库
  await optimizeDatabase()

  console.log('Database cleanup completed')
}

// 默认导出便捷对象
export default {
  // 初始化和管理
  initialize: initializeDatabase,
  close: closeDatabase,
  optimize: optimizeDatabase,
  cleanup: cleanupDatabase,
  getStats: getDatabaseStats,

  // 模块
  tabs,
  settings,
  window
}
