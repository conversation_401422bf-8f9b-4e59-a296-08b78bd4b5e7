/**
 * 数据库模块主入口
 * 提供模块化的数据库功能
 */

import CoreDatabaseManager from './core/database-manager'
import { tabs, settings, window } from './modules'

// 统一导出所有模块
export * from './modules'

/**
 * 初始化数据库
 * 必须在使用任何数据库功能之前调用
 */
export async function initializeDatabase(): Promise<void> {
  const dbManager = CoreDatabaseManager.getInstance()
  await dbManager.initialize()

  // 初始化各模块的默认数据
  await settings.initDefaults()
  await window.initialize()
}

/**
 * 关闭数据库连接
 */
export async function closeDatabase(): Promise<void> {
  const dbManager = CoreDatabaseManager.getInstance()
  await dbManager.close()
}

/**
 * 获取数据库统计信息
 */
export function getDatabaseStats() {
  const dbManager = CoreDatabaseManager.getInstance()
  return dbManager.getStats()
}

/**
 * 优化数据库
 */
export async function optimizeDatabase(): Promise<void> {
  const dbManager = CoreDatabaseManager.getInstance()
  await dbManager.optimize()
}

/**
 * 清理数据库
 */
export async function cleanupDatabase(): Promise<void> {
  // 清理旧的标签页记录
  await tabs.cleanup(30)

  // 清理空的设置
  await settings.cleanup()

  // 优化数据库
  await optimizeDatabase()

  console.log('Database cleanup completed')
}

// 默认导出便捷对象
export default {
  // 初始化和管理
  initialize: initializeDatabase,
  close: closeDatabase,
  optimize: optimizeDatabase,
  cleanup: cleanupDatabase,
  getStats: getDatabaseStats
}
