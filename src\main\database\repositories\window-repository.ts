import type Database from 'better-sqlite3'
import type { DatabaseWindowState } from '../../../shared/types'

export class WindowRepository {
  private db: Database.Database

  constructor(database: Database.Database) {
    this.db = database
  }

  /**
   * 保存窗口状态
   */
  async saveWindowState(state: Omit<DatabaseWindowState, 'id' | 'updatedAt'>): Promise<void> {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO window_state (
        id, isMaximized, isMinimized, isFullscreen, isFocused, bounds, updatedAt
      ) VALUES (1, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
      state.isMaximized,
      state.isMinimized,
      state.isFullscreen,
      state.isFocused,
      state.bounds,
      new Date().toISOString()
    )
  }

  /**
   * 获取窗口状态
   */
  async getWindowState(): Promise<DatabaseWindowState | undefined> {
    const stmt = this.db.prepare('SELECT * FROM window_state WHERE id = 1')
    return stmt.get() as DatabaseWindowState | undefined
  }

  /**
   * 更新窗口边界
   */
  async updateWindowBounds(bounds: string): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE window_state SET bounds = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(bounds, new Date().toISOString())
  }

  /**
   * 更新窗口最大化状态
   */
  async updateMaximizedState(isMaximized: boolean): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isMaximized = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isMaximized, new Date().toISOString())
  }

  /**
   * 更新窗口最小化状态
   */
  async updateMinimizedState(isMinimized: boolean): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isMinimized = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isMinimized, new Date().toISOString())
  }

  /**
   * 更新窗口全屏状态
   */
  async updateFullscreenState(isFullscreen: boolean): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isFullscreen = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isFullscreen, new Date().toISOString())
  }

  /**
   * 更新窗口焦点状态
   */
  async updateFocusState(isFocused: boolean): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isFocused = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isFocused, new Date().toISOString())
  }

  /**
   * 清除窗口状态
   */
  async clearWindowState(): Promise<void> {
    const stmt = this.db.prepare('DELETE FROM window_state WHERE id = 1')
    stmt.run()
  }
}
