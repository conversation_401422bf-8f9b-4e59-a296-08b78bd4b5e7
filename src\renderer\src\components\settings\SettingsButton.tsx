import React, { useState } from 'react'
import { Settings } from 'lucide-react'
import { cn } from '@/lib/utils'
import { SettingsDialog } from './SettingsDialog'

interface SettingsButtonProps {
  className?: string
}

export function SettingsButton({ className }: SettingsButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <button
        onClick={() => setIsDialogOpen(true)}
        className={cn(
          "p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors",
          "text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200",
          className
        )}
        title="设置"
      >
        <Settings className="w-4 h-4" />
      </button>

      <SettingsDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
      />
    </>
  )
}
