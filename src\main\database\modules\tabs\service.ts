import type { Tab } from '../../../../shared/types'
import { TabRepository } from './repository'

/**
 * 标签页业务服务
 */
export class TabService {
  private static instance: TabService
  private repository: TabRepository

  private constructor() {
    this.repository = TabRepository.getInstance()
  }

  static getInstance(): TabService {
    if (!TabService.instance) {
      TabService.instance = new TabService()
    }
    return TabService.instance
  }

  /**
   * 保存标签页数据
   */
  async saveTabs(tabs: Tab[]): Promise<void> {
    // 验证标签页数据
    this.validateTabs(tabs)

    this.repository.saveTabs(tabs)
  }

  /**
   * 获取所有标签页
   */
  async getTabs(): Promise<Tab[]> {
    return this.repository.getTabs()
  }

  /**
   * 根据ID获取标签页
   */
  async getTabById(id: string): Promise<Tab | undefined> {
    if (!id) {
      throw new Error('Tab ID is required')
    }
    return this.repository.getTabById(id)
  }

  /**
   * 创建新标签页
   */
  async createTab(tabData: Omit<Tab, 'lastModified'>): Promise<void> {
    // 验证标签页数据
    this.validateTabData(tabData)

    // 检查ID是否已存在
    if (this.repository.tabExists(tabData.id)) {
      throw new Error(`Tab with ID '${tabData.id}' already exists`)
    }

    // 如果新标签页是活跃的，需要先取消其他标签页的活跃状态
    if (tabData.active) {
      this.repository.setActiveTab(tabData.id)
    }

    this.repository.insertTab(tabData as Tab)
  }

  /**
   * 更新标签页
   */
  async updateTab(id: string, updates: Partial<Tab>): Promise<void> {
    if (!id) {
      throw new Error('Tab ID is required')
    }

    // 检查标签页是否存在
    if (!this.repository.tabExists(id)) {
      throw new Error(`Tab with ID '${id}' not found`)
    }

    // 如果要激活这个标签页，使用专门的方法
    if (updates.active === true) {
      this.repository.setActiveTab(id)
      // 移除 active 字段，因为已经通过 setActiveTab 处理了
      const { active, ...otherUpdates } = updates
      if (Object.keys(otherUpdates).length > 0) {
        this.repository.updateTab(id, otherUpdates)
      }
    } else {
      this.repository.updateTab(id, updates)
    }
  }

  /**
   * 删除标签页
   */
  async deleteTab(id: string): Promise<void> {
    if (!id) {
      throw new Error('Tab ID is required')
    }

    // 检查是否为首页标签页
    const tab = this.repository.getTabById(id)
    if (tab && tab.type === 'home') {
      throw new Error('Cannot delete home tab')
    }

    const deleted = this.repository.deleteTab(id)
    if (!deleted) {
      throw new Error(`Tab with ID '${id}' not found`)
    }

    // 如果删除的是活跃标签页，需要激活另一个标签页
    if (tab && tab.active) {
      const remainingTabs = this.repository.getTabs()
      if (remainingTabs.length > 0) {
        // 激活第一个标签页
        this.repository.setActiveTab(remainingTabs[0].id)
      }
    }
  }

  /**
   * 获取活跃标签页
   */
  async getActiveTab(): Promise<Tab | undefined> {
    return this.repository.getActiveTab()
  }

  /**
   * 设置活跃标签页
   */
  async setActiveTab(id: string): Promise<void> {
    if (!id) {
      throw new Error('Tab ID is required')
    }

    if (!this.repository.tabExists(id)) {
      throw new Error(`Tab with ID '${id}' not found`)
    }

    this.repository.setActiveTab(id)
  }

  /**
   * 获取固定标签页
   */
  async getPinnedTabs(): Promise<Tab[]> {
    return this.repository.getPinnedTabs()
  }

  /**
   * 获取未保存的标签页
   */
  async getDirtyTabs(): Promise<Tab[]> {
    return this.repository.getDirtyTabs()
  }

  /**
   * 根据类型获取标签页
   */
  async getTabsByType(type: string): Promise<Tab[]> {
    if (!type) {
      throw new Error('Tab type is required')
    }
    return this.repository.getTabsByType(type)
  }

  /**
   * 清理旧的标签页记录
   */
  async cleanupOldTabs(daysToKeep: number = 30): Promise<number> {
    if (daysToKeep < 1) {
      throw new Error('Days to keep must be at least 1')
    }
    return this.repository.cleanupOldTabs(daysToKeep)
  }

  /**
   * 获取标签页统计信息
   */
  async getTabStats(): Promise<{
    total: number
    active: number
    pinned: number
    dirty: number
    byType: Record<string, number>
  }> {
    return this.repository.getTabStats()
  }

  /**
   * 验证标签页数组
   */
  private validateTabs(tabs: Tab[]): void {
    if (!Array.isArray(tabs)) {
      throw new Error('Tabs must be an array')
    }

    for (const tab of tabs) {
      this.validateTabData(tab)
    }

    // 确保只有一个活跃标签页
    const activeTabs = tabs.filter((tab) => tab.active)
    if (activeTabs.length > 1) {
      throw new Error('Only one tab can be active at a time')
    }
  }

  /**
   * 验证单个标签页数据
   */
  private validateTabData(tab: Partial<Tab>): void {
    if (!tab.id) {
      throw new Error('Tab ID is required')
    }
    if (!tab.title) {
      throw new Error('Tab title is required')
    }
    if (!tab.type) {
      throw new Error('Tab type is required')
    }

    // 验证布尔值字段
    if (tab.active !== undefined && typeof tab.active !== 'boolean') {
      throw new Error('Tab active must be a boolean')
    }
    if (tab.isDirty !== undefined && typeof tab.isDirty !== 'boolean') {
      throw new Error('Tab isDirty must be a boolean')
    }
    if (tab.isPinned !== undefined && typeof tab.isPinned !== 'boolean') {
      throw new Error('Tab isPinned must be a boolean')
    }
    if (tab.closable !== undefined && typeof tab.closable !== 'boolean') {
      throw new Error('Tab closable must be a boolean')
    }

    // 验证字符串长度
    if (tab.title && tab.title.length > 255) {
      throw new Error('Tab title is too long (max 255 characters)')
    }
    if (tab.tooltip && tab.tooltip.length > 500) {
      throw new Error('Tab tooltip is too long (max 500 characters)')
    }
  }
}
