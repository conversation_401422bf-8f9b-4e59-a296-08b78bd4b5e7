import React, { useState, useCallback } from 'react'
import { X, Save, RotateCcw, Settings as SettingsIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSettings } from '@/hooks/useSettings'
import type { AppSettings } from '@/types'

interface SettingsDialogProps {
  isOpen: boolean
  onClose: () => void
}

export function SettingsDialog({ isOpen, onClose }: SettingsDialogProps) {
  const { settings, isLoading, error, saveSettings, resetSettings } = useSettings()
  const [localSettings, setLocalSettings] = useState<AppSettings>(settings)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // 更新本地设置
  const updateLocalSetting = useCallback(<K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ) => {
    setLocalSettings(prev => {
      const newSettings = { ...prev, [key]: value }
      setHasChanges(JSON.stringify(newSettings) !== JSON.stringify(settings))
      return newSettings
    })
  }, [settings])

  // 保存设置
  const handleSave = useCallback(async () => {
    if (!hasChanges) return

    setIsSaving(true)
    try {
      const success = await saveSettings(localSettings)
      if (success) {
        setHasChanges(false)
        onClose()
      }
    } finally {
      setIsSaving(false)
    }
  }, [hasChanges, localSettings, saveSettings, onClose])

  // 重置设置
  const handleReset = useCallback(async () => {
    setIsSaving(true)
    try {
      const success = await resetSettings()
      if (success) {
        setHasChanges(false)
        onClose()
      }
    } finally {
      setIsSaving(false)
    }
  }, [resetSettings, onClose])

  // 取消更改
  const handleCancel = useCallback(() => {
    setLocalSettings(settings)
    setHasChanges(false)
    onClose()
  }, [settings, onClose])

  // 同步外部设置变化
  React.useEffect(() => {
    if (!hasChanges) {
      setLocalSettings(settings)
    }
  }, [settings, hasChanges])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <SettingsIcon className="w-5 h-5" />
            <h2 className="text-lg font-semibold">应用设置</h2>
          </div>
          <button
            onClick={handleCancel}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500">加载设置中...</div>
            </div>
          ) : error ? (
            <div className="text-red-500 text-center py-8">
              加载设置失败: {error}
            </div>
          ) : (
            <div className="space-y-6">
              {/* 状态保存设置 */}
              <section>
                <h3 className="text-base font-medium mb-4">状态保存</h3>
                <div className="space-y-4">
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={localSettings.saveAppState}
                      onChange={(e) => updateLocalSetting('saveAppState', e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <div>
                      <div className="font-medium">保存应用状态</div>
                      <div className="text-sm text-gray-500">
                        退出时自动保存应用状态，下次启动时恢复
                      </div>
                    </div>
                  </label>

                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={localSettings.restoreWindowState}
                      onChange={(e) => updateLocalSetting('restoreWindowState', e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                      disabled={!localSettings.saveAppState}
                    />
                    <div>
                      <div className="font-medium">恢复窗口状态</div>
                      <div className="text-sm text-gray-500">
                        启动时恢复窗口大小和位置
                      </div>
                    </div>
                  </label>

                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={localSettings.restoreTabs}
                      onChange={(e) => updateLocalSetting('restoreTabs', e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                      disabled={!localSettings.saveAppState}
                    />
                    <div>
                      <div className="font-medium">恢复标签页</div>
                      <div className="text-sm text-gray-500">
                        启动时恢复上次打开的标签页
                      </div>
                    </div>
                  </label>
                </div>
              </section>

              {/* 标签页设置 */}
              <section>
                <h3 className="text-base font-medium mb-4">标签页</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      最大最近标签页数量
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="20"
                      value={localSettings.maxRecentTabs}
                      onChange={(e) => updateLocalSetting('maxRecentTabs', parseInt(e.target.value) || 10)}
                      className="w-24 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                    />
                    <div className="text-sm text-gray-500 mt-1">
                      保留的最近打开标签页数量 (1-20)
                    </div>
                  </div>
                </div>
              </section>

              {/* 外观设置 */}
              <section>
                <h3 className="text-base font-medium mb-4">外观</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">主题</label>
                    <select
                      value={localSettings.theme}
                      onChange={(e) => updateLocalSetting('theme', e.target.value as 'light' | 'dark' | 'system')}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                    >
                      <option value="system">跟随系统</option>
                      <option value="light">浅色</option>
                      <option value="dark">深色</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">语言</label>
                    <select
                      value={localSettings.language}
                      onChange={(e) => updateLocalSetting('language', e.target.value)}
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700"
                    >
                      <option value="zh-CN">简体中文</option>
                      <option value="en-US">English</option>
                    </select>
                  </div>
                </div>
              </section>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleReset}
            disabled={isSaving}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
            重置默认
          </button>

          <div className="flex items-center gap-2">
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
              className={cn(
                "flex items-center gap-2 px-4 py-2 rounded-md transition-colors",
                hasChanges && !isSaving
                  ? "bg-blue-600 hover:bg-blue-700 text-white"
                  : "bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed"
              )}
            >
              <Save className="w-4 h-4" />
              {isSaving ? '保存中...' : '保存'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
