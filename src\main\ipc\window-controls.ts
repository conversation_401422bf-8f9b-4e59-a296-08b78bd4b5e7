import { BrowserWindow, ipc<PERSON>ain, <PERSON>u, dialog } from 'electron'
import type { ApiResponse } from '../../shared/types'
import { createApiResponse } from '../../shared/types'

export function setupWindowControlsIPC(mainWindow: BrowserWindow): void {
  // 窗口最小化
  ipcMain.handle('window:minimize', async (): Promise<ApiResponse> => {
    try {
      mainWindow.minimize()
      return createApiResponse(true)
    } catch (error) {
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : '未知错误'
      )
    }
  })

  // 窗口最大化/还原
  ipcMain.handle('window:maximize', async (): Promise<ApiResponse<{ isMaximized: boolean }>> => {
    try {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize()
        return createApiResponse(true, { isMaximized: false })
      } else {
        mainWindow.maximize()
        return createApiResponse(true, { isMaximized: true })
      }
    } catch (error) {
      return createApiResponse(
        false,
        { isMaximized: false },
        error instanceof Error ? error.message : '未知错误'
      )
    }
  })

  // 窗口关闭
  ipcMain.handle('window:close', async (): Promise<ApiResponse> => {
    try {
      // 检查是否有未保存的更改
      const hasUnsavedChanges = await mainWindow.webContents.executeJavaScript(`
        window.electronAPI?.hasUnsavedChanges?.() || false
      `)

      if (hasUnsavedChanges) {
        const result = await dialog.showMessageBox(mainWindow, {
          type: 'warning',
          buttons: ['保存并关闭', '不保存关闭', '取消'],
          defaultId: 0,
          cancelId: 2,
          title: '确认关闭',
          message: '您有未保存的更改，是否要保存？'
        })

        switch (result.response) {
          case 0: // 保存并关闭
            await mainWindow.webContents.executeJavaScript(`
              window.electronAPI?.saveAll?.()
            `)
            mainWindow.close()
            break
          case 1: // 不保存关闭
            mainWindow.close()
            break
          case 2: // 取消
            return createApiResponse(false, undefined, 'User cancelled')
        }
      } else {
        mainWindow.close()
      }

      return createApiResponse(true)
    } catch (error) {
      return createApiResponse(
        false,
        undefined,
        error instanceof Error ? error.message : '未知错误'
      )
    }
  })

  // 获取窗口状态
  ipcMain.handle('window:get-state', async () => {
    try {
      const bounds = mainWindow.getBounds()
      return {
        success: true,
        data: {
          isMaximized: mainWindow.isMaximized(),
          isMinimized: mainWindow.isMinimized(),
          isFullscreen: mainWindow.isFullScreen(),
          isFocused: mainWindow.isFocused(),
          bounds
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 设置窗口标题
  ipcMain.handle('window:set-title', async (_, title: string) => {
    try {
      mainWindow.setTitle(title)
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 显示标题栏右键菜单
  ipcMain.handle('titlebar:show-context-menu', async (_, x: number, y: number) => {
    try {
      const template = [
        {
          label: mainWindow.isMaximized() ? '还原' : '最大化',
          click: () => {
            if (mainWindow.isMaximized()) {
              mainWindow.unmaximize()
            } else {
              mainWindow.maximize()
            }
          }
        },
        {
          label: '最小化',
          click: () => mainWindow.minimize()
        },
        { type: 'separator' as const },
        {
          label: '关闭',
          click: () => mainWindow.close()
        }
      ]

      const menu = Menu.buildFromTemplate(template)
      menu.popup({
        window: mainWindow,
        x: Math.round(x),
        y: Math.round(y)
      })

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 设置窗口边界
  ipcMain.handle(
    'window:set-bounds',
    async (_, bounds: { x: number; y: number; width: number; height: number }) => {
      try {
        mainWindow.setBounds(bounds)
        return { success: true }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        }
      }
    }
  )

  // 居中窗口
  ipcMain.handle('window:center', async () => {
    try {
      mainWindow.center()
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })
}
