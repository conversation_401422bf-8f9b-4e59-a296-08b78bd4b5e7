import type Database from 'better-sqlite3'
import type { DatabaseSettings } from '../../../shared/types'

export class SettingsRepository {
  private db: Database.Database

  constructor(database: Database.Database) {
    this.db = database
  }

  /**
   * 保存设置
   */
  async saveSetting(key: string, value: any): Promise<void> {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updatedAt)
      VALUES (?, ?, ?)
    `)

    stmt.run(key, JSON.stringify(value), new Date().toISOString())
  }

  /**
   * 获取设置
   */
  async getSetting<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?')
    const result = stmt.get(key) as DatabaseSettings | undefined

    if (result) {
      try {
        return JSON.parse(result.value)
      } catch (error) {
        console.error('Failed to parse setting value:', error)
        return defaultValue
      }
    }

    return defaultValue
  }

  /**
   * 获取所有设置
   */
  async getAllSettings(): Promise<Record<string, any>> {
    const stmt = this.db.prepare('SELECT key, value FROM settings')
    const results = stmt.all() as DatabaseSettings[]

    const settings: Record<string, any> = {}
    for (const result of results) {
      try {
        settings[result.key] = JSON.parse(result.value)
      } catch (error) {
        console.error(`Failed to parse setting ${result.key}:`, error)
      }
    }

    return settings
  }

  /**
   * 删除设置
   */
  async deleteSetting(key: string): Promise<void> {
    const stmt = this.db.prepare('DELETE FROM settings WHERE key = ?')
    stmt.run(key)
  }

  /**
   * 批量保存设置
   */
  async saveSettings(settings: Record<string, any>): Promise<void> {
    const transaction = this.db.transaction(() => {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO settings (key, value, updatedAt)
        VALUES (?, ?, ?)
      `)

      const now = new Date().toISOString()
      for (const [key, value] of Object.entries(settings)) {
        stmt.run(key, JSON.stringify(value), now)
      }
    })

    transaction()
  }

  /**
   * 检查设置是否存在
   */
  async hasSetting(key: string): Promise<boolean> {
    const stmt = this.db.prepare('SELECT 1 FROM settings WHERE key = ? LIMIT 1')
    return stmt.get(key) !== undefined
  }

  /**
   * 获取设置的更新时间
   */
  async getSettingUpdatedAt(key: string): Promise<string | undefined> {
    const stmt = this.db.prepare('SELECT updatedAt FROM settings WHERE key = ?')
    const result = stmt.get(key) as { updatedAt: string } | undefined
    return result?.updatedAt
  }
}
