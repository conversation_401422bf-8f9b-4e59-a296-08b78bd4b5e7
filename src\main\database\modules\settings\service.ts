import { SettingsRepository } from './repository'

/**
 * 设置业务服务
 */
export class SettingsService {
  private static instance: SettingsService
  private repository: SettingsRepository

  private constructor() {
    this.repository = SettingsRepository.getInstance()
  }

  static getInstance(): SettingsService {
    if (!SettingsService.instance) {
      SettingsService.instance = new SettingsService()
    }
    return SettingsService.instance
  }

  /**
   * 保存设置
   */
  async saveSetting(key: string, value: any): Promise<void> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    // 验证设置值
    this.validateSettingValue(value)

    // 序列化值
    const serializedValue = JSON.stringify(value)
    this.repository.saveSetting(key, serializedValue)
  }

  /**
   * 获取设置
   */
  async getSetting<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    const value = this.repository.getSettingValue(key)
    if (value !== undefined) {
      try {
        return JSON.parse(value)
      } catch (error) {
        console.error('Failed to parse setting value:', error)
        return defaultValue
      }
    }

    return defaultValue
  }

  /**
   * 获取所有设置
   */
  async getAllSettings(): Promise<Record<string, any>> {
    const settingsMap = this.repository.getAllSettingsMap()
    const settings: Record<string, any> = {}

    for (const [key, value] of Object.entries(settingsMap)) {
      try {
        settings[key] = JSON.parse(value)
      } catch (error) {
        console.error(`Failed to parse setting ${key}:`, error)
        settings[key] = value // 保留原始值
      }
    }

    return settings
  }

  /**
   * 删除设置
   */
  async deleteSetting(key: string): Promise<boolean> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    return this.repository.deleteSetting(key)
  }

  /**
   * 批量保存设置
   */
  async saveSettings(settings: Record<string, any>): Promise<void> {
    if (!settings || typeof settings !== 'object') {
      throw new Error('Settings must be an object')
    }

    // 验证所有设置值
    for (const [key, value] of Object.entries(settings)) {
      if (!key) {
        throw new Error('Setting key cannot be empty')
      }
      this.validateSettingValue(value)
    }

    // 序列化所有值
    const serializedSettings: Record<string, string> = {}
    for (const [key, value] of Object.entries(settings)) {
      serializedSettings[key] = JSON.stringify(value)
    }

    this.repository.saveSettings(serializedSettings)
  }

  /**
   * 批量删除设置
   */
  async deleteSettings(keys: string[]): Promise<number> {
    if (!Array.isArray(keys)) {
      throw new Error('Keys must be an array')
    }

    return this.repository.deleteSettings(keys)
  }

  /**
   * 检查设置是否存在
   */
  async hasSetting(key: string): Promise<boolean> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    return this.repository.hasSetting(key)
  }

  /**
   * 获取设置的更新时间
   */
  async getSettingUpdatedAt(key: string): Promise<string | undefined> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    return this.repository.getSettingUpdatedAt(key)
  }

  /**
   * 根据前缀获取设置
   */
  async getSettingsByPrefix(prefix: string): Promise<Record<string, any>> {
    if (!prefix) {
      throw new Error('Prefix is required')
    }

    const settings = this.repository.getSettingsByPrefix(prefix)
    const result: Record<string, any> = {}

    for (const setting of settings) {
      try {
        result[setting.key] = JSON.parse(setting.value)
      } catch (error) {
        console.error(`Failed to parse setting ${setting.key}:`, error)
        result[setting.key] = setting.value
      }
    }

    return result
  }

  /**
   * 根据前缀删除设置
   */
  async deleteSettingsByPrefix(prefix: string): Promise<number> {
    if (!prefix) {
      throw new Error('Prefix is required')
    }

    return this.repository.deleteSettingsByPrefix(prefix)
  }

  /**
   * 重置设置到默认值
   */
  async resetSetting(key: string, defaultValue?: any): Promise<void> {
    if (!key) {
      throw new Error('Setting key is required')
    }

    if (defaultValue !== undefined) {
      await this.saveSetting(key, defaultValue)
    } else {
      await this.deleteSetting(key)
    }
  }

  /**
   * 获取应用程序默认设置
   */
  getDefaultSettings(): Record<string, any> {
    return {
      // 应用程序设置
      saveAppState: true,
      restoreTabs: true,
      autoSave: true,
      autoSaveInterval: 30000, // 30秒

      // 界面设置
      theme: 'light',
      fontSize: 14,
      fontFamily: 'system-ui',
      
      // 标题栏设置
      showTitlebar: true,
      enableVirtualization: true,
      maxVisibleTabs: 10,
      
      // 窗口设置
      rememberWindowState: true,
      startMaximized: false,
      
      // 编辑器设置
      wordWrap: true,
      lineNumbers: true,
      minimap: false,
      
      // 其他设置
      checkForUpdates: true,
      enableTelemetry: false
    }
  }

  /**
   * 初始化默认设置
   */
  async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = this.getDefaultSettings()
    const existingSettings = await this.getAllSettings()

    // 只设置不存在的设置
    const settingsToSet: Record<string, any> = {}
    for (const [key, value] of Object.entries(defaultSettings)) {
      if (!(key in existingSettings)) {
        settingsToSet[key] = value
      }
    }

    if (Object.keys(settingsToSet).length > 0) {
      await this.saveSettings(settingsToSet)
    }
  }

  /**
   * 获取设置统计信息
   */
  async getSettingsStats(): Promise<{
    total: number
    byPrefix: Record<string, number>
    lastUpdated: string | null
  }> {
    return this.repository.getSettingsStats()
  }

  /**
   * 清理空值设置
   */
  async cleanupEmptySettings(): Promise<number> {
    return this.repository.cleanupEmptySettings()
  }

  /**
   * 搜索设置
   */
  async searchSettings(query: string): Promise<Record<string, any>> {
    if (!query) {
      throw new Error('Search query is required')
    }

    const settings = this.repository.searchSettings(query)
    const result: Record<string, any> = {}

    for (const setting of settings) {
      try {
        result[setting.key] = JSON.parse(setting.value)
      } catch (error) {
        console.error(`Failed to parse setting ${setting.key}:`, error)
        result[setting.key] = setting.value
      }
    }

    return result
  }

  /**
   * 验证设置值
   */
  private validateSettingValue(value: any): void {
    // 检查是否可以序列化为JSON
    try {
      JSON.stringify(value)
    } catch (error) {
      throw new Error('Setting value must be JSON serializable')
    }

    // 检查值的大小（防止存储过大的数据）
    const serialized = JSON.stringify(value)
    if (serialized.length > 1024 * 1024) { // 1MB限制
      throw new Error('Setting value is too large (max 1MB)')
    }
  }
}
