import type { DatabaseSettings } from '../../../../shared/types'
import { DatabaseSettingsSchema } from '../../../../shared/types'
import { BaseRepository } from '../../core/base-repository'

/**
 * 设置数据仓库
 */
export class SettingsRepository extends BaseRepository {
  private static instance: SettingsRepository

  static getInstance(): SettingsRepository {
    if (!SettingsRepository.instance) {
      SettingsRepository.instance = new SettingsRepository()
    }
    return SettingsRepository.instance
  }

  /**
   * 保存设置
   */
  saveSetting(key: string, value: string): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updatedAt)
      VALUES (?, ?, ?)
    `)

    stmt.run(key, value, new Date().toISOString())
  }

  /**
   * 获取设置
   */
  getSetting(key: string): DatabaseSettings | undefined {
    const stmt = this.db.prepare('SELECT * FROM settings WHERE key = ?')
    return stmt.get(key) as DatabaseSettings | undefined
  }

  /**
   * 获取设置值
   */
  getSettingValue(key: string): string | undefined {
    const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?')
    const result = stmt.get(key) as { value: string } | undefined
    return result?.value
  }

  /**
   * 获取所有设置
   */
  getAllSettings(): DatabaseSettings[] {
    const stmt = this.db.prepare('SELECT * FROM settings ORDER BY key')
    return stmt.all() as DatabaseSettings[]
  }

  /**
   * 获取所有设置的键值对
   */
  getAllSettingsMap(): Record<string, string> {
    const stmt = this.db.prepare('SELECT key, value FROM settings')
    const results = stmt.all() as { key: string; value: string }[]

    const settings: Record<string, string> = {}
    for (const result of results) {
      settings[result.key] = result.value
    }

    return settings
  }

  /**
   * 删除设置
   */
  deleteSetting(key: string): boolean {
    const stmt = this.db.prepare('DELETE FROM settings WHERE key = ?')
    const result = stmt.run(key)
    return result.changes > 0
  }

  /**
   * 批量保存设置
   */
  saveSettings(settings: Record<string, string>): void {
    this.transaction(() => {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO settings (key, value, updatedAt)
        VALUES (?, ?, ?)
      `)

      const now = new Date().toISOString()
      for (const [key, value] of Object.entries(settings)) {
        stmt.run(key, value, now)
      }
    })
  }

  /**
   * 批量删除设置
   */
  deleteSettings(keys: string[]): number {
    if (keys.length === 0) return 0

    const placeholders = keys.map(() => '?').join(', ')
    const stmt = this.db.prepare(`DELETE FROM settings WHERE key IN (${placeholders})`)
    const result = stmt.run(...keys)
    return result.changes
  }

  /**
   * 检查设置是否存在
   */
  hasSetting(key: string): boolean {
    const stmt = this.db.prepare('SELECT 1 FROM settings WHERE key = ? LIMIT 1')
    return stmt.get(key) !== undefined
  }

  /**
   * 获取设置的更新时间
   */
  getSettingUpdatedAt(key: string): string | undefined {
    const stmt = this.db.prepare('SELECT updatedAt FROM settings WHERE key = ?')
    const result = stmt.get(key) as { updatedAt: string } | undefined
    return result?.updatedAt
  }

  /**
   * 根据前缀获取设置
   */
  getSettingsByPrefix(prefix: string): DatabaseSettings[] {
    const stmt = this.db.prepare('SELECT * FROM settings WHERE key LIKE ? ORDER BY key')
    return stmt.all(`${prefix}%`) as DatabaseSettings[]
  }

  /**
   * 根据前缀删除设置
   */
  deleteSettingsByPrefix(prefix: string): number {
    const stmt = this.db.prepare('DELETE FROM settings WHERE key LIKE ?')
    const result = stmt.run(`${prefix}%`)
    return result.changes
  }

  /**
   * 获取设置统计信息
   */
  getSettingsStats(): {
    total: number
    byPrefix: Record<string, number>
    lastUpdated: string | null
  } {
    const total = this.getTableRowCount('settings')
    const lastUpdated = this.getLastUpdated('settings')

    // 按前缀分组统计（以第一个点分割）
    const stmt = this.db.prepare(`
      SELECT 
        CASE 
          WHEN key LIKE '%.%' THEN SUBSTR(key, 1, INSTR(key, '.') - 1)
          ELSE key
        END as prefix,
        COUNT(*) as count
      FROM settings 
      GROUP BY prefix
      ORDER BY prefix
    `)

    const prefixResults = stmt.all() as { prefix: string; count: number }[]
    const byPrefix: Record<string, number> = {}
    prefixResults.forEach((result) => {
      byPrefix[result.prefix] = result.count
    })

    return {
      total,
      byPrefix,
      lastUpdated
    }
  }

  /**
   * 清理空值设置
   */
  cleanupEmptySettings(): number {
    const stmt = this.db.prepare("DELETE FROM settings WHERE value = '' OR value IS NULL")
    const result = stmt.run()
    return result.changes
  }

  /**
   * 搜索设置
   */
  searchSettings(query: string): DatabaseSettings[] {
    const stmt = this.db.prepare(`
      SELECT * FROM settings 
      WHERE key LIKE ? OR value LIKE ?
      ORDER BY key
    `)
    const searchPattern = `%${query}%`
    return stmt.all(searchPattern, searchPattern) as DatabaseSettings[]
  }
}
