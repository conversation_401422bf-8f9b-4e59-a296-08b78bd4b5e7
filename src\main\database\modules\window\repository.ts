import type { DatabaseWindowState } from '../../../../shared/types'
import { BaseRepository } from '../../core/base-repository'

/**
 * 窗口状态数据仓库
 */
export class WindowRepository extends BaseRepository {
  private static instance: WindowRepository

  static getInstance(): WindowRepository {
    if (!WindowRepository.instance) {
      WindowRepository.instance = new WindowRepository()
    }
    return WindowRepository.instance
  }

  /**
   * 保存窗口状态
   */
  saveWindowState(state: Omit<DatabaseWindowState, 'id' | 'updatedAt'>): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO window_state (
        id, isMaximized, isMinimized, isFullscreen, isFocused, bounds, updatedAt
      ) VALUES (1, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
      state.isMaximized,
      state.isMinimized,
      state.isFullscreen,
      state.isFocused,
      state.bounds,
      new Date().toISOString()
    )
  }

  /**
   * 获取窗口状态
   */
  getWindowState(): DatabaseWindowState | undefined {
    const stmt = this.db.prepare('SELECT * FROM window_state WHERE id = 1')
    return stmt.get() as DatabaseWindowState | undefined
  }

  /**
   * 更新窗口边界
   */
  updateWindowBounds(bounds: string): void {
    const stmt = this.db.prepare(`
      UPDATE window_state SET bounds = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(bounds, new Date().toISOString())
  }

  /**
   * 更新窗口最大化状态
   */
  updateMaximizedState(isMaximized: number): void {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isMaximized = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isMaximized, new Date().toISOString())
  }

  /**
   * 更新窗口最小化状态
   */
  updateMinimizedState(isMinimized: number): void {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isMinimized = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isMinimized, new Date().toISOString())
  }

  /**
   * 更新窗口全屏状态
   */
  updateFullscreenState(isFullscreen: number): void {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isFullscreen = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isFullscreen, new Date().toISOString())
  }

  /**
   * 更新窗口焦点状态
   */
  updateFocusState(isFocused: number): void {
    const stmt = this.db.prepare(`
      UPDATE window_state SET isFocused = ?, updatedAt = ? WHERE id = 1
    `)
    stmt.run(isFocused, new Date().toISOString())
  }

  /**
   * 清除窗口状态
   */
  clearWindowState(): void {
    const stmt = this.db.prepare('DELETE FROM window_state WHERE id = 1')
    stmt.run()
  }

  /**
   * 检查窗口状态是否存在
   */
  windowStateExists(): boolean {
    const stmt = this.db.prepare('SELECT 1 FROM window_state WHERE id = 1 LIMIT 1')
    return stmt.get() !== undefined
  }

  /**
   * 获取窗口状态的更新时间
   */
  getWindowStateUpdatedAt(): string | undefined {
    const stmt = this.db.prepare('SELECT updatedAt FROM window_state WHERE id = 1')
    const result = stmt.get() as { updatedAt: string } | undefined
    return result?.updatedAt
  }

  /**
   * 批量更新窗口状态字段
   */
  updateWindowStateFields(updates: Partial<Omit<DatabaseWindowState, 'id' | 'updatedAt'>>): void {
    const fields = Object.keys(updates)
    if (fields.length === 0) return

    const setClause = fields.map((field) => `${field} = ?`).join(', ')
    const values = fields.map((field) => updates[field as keyof typeof updates])

    const stmt = this.db.prepare(`
      UPDATE window_state SET ${setClause}, updatedAt = ? WHERE id = 1
    `)

    stmt.run(...values, new Date().toISOString())
  }

  /**
   * 初始化默认窗口状态
   */
  initializeDefaultWindowState(): void {
    if (!this.windowStateExists()) {
      const defaultState = {
        isMaximized: 0,
        isMinimized: 0,
        isFullscreen: 0,
        isFocused: 1,
        bounds: JSON.stringify({
          x: 100,
          y: 100,
          width: 1200,
          height: 800
        })
      }

      this.saveWindowState(defaultState)
    }
  }
}
