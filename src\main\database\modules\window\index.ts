/**
 * 窗口状态模块
 * 提供窗口状态相关的所有功能
 */

import { WindowService } from './service'

// 获取服务实例
const windowService = WindowService.getInstance()

// 导出便捷方法
const window = {
  // 基础操作
  save: windowService.saveWindowState.bind(windowService),
  get: windowService.getWindowState.bind(windowService),
  clear: windowService.clearWindowState.bind(windowService),

  // 初始化
  initialize: windowService.initializeWindowState.bind(windowService)
}

// 默认导出便捷对象
export default window
