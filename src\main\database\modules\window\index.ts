/**
 * 窗口状态模块
 * 提供窗口状态相关的所有功能
 */

import { WindowRepository } from './repository'
import { WindowService } from './service'

// 导出单例实例
export const windowRepository = WindowRepository.getInstance()
export const windowService = WindowService.getInstance()

// 导出类型（如果需要）
export { WindowRepository, WindowService }

// 导出便捷方法
export const window = {
  // 基础操作
  save: windowService.saveWindowState.bind(windowService),
  get: windowService.getWindowState.bind(windowService),
  clear: windowService.clearWindowState.bind(windowService),
  exists: windowService.windowStateExists.bind(windowService),

  // 状态更新
  updateBounds: windowService.updateWindowBounds.bind(windowService),
  updateMaximized: windowService.updateMaximizedState.bind(windowService),
  updateMinimized: windowService.updateMinimizedState.bind(windowService),
  updateFullscreen: windowService.updateFullscreenState.bind(windowService),
  updateFocus: windowService.updateFocusState.bind(windowService),
  updateFields: windowService.updateWindowStateFields.bind(windowService),

  // 工具方法
  getDefault: windowService.getDefaultWindowState.bind(windowService),
  parseBounds: windowService.parseWindowBounds.bind(windowService),
  getUpdatedAt: windowService.getWindowStateUpdatedAt.bind(windowService),

  // 初始化
  initialize: windowService.initializeWindowState.bind(windowService)
}

// 默认导出便捷对象
export default window
