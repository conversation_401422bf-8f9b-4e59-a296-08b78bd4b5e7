import { useState, useCallback } from 'react'
import type { ApiResponse, WindowState } from '@/types'

export function useWindowControls() {
  const [isLoading, setIsLoading] = useState(false)

  const minimize = useCallback(async (): Promise<ApiResponse> => {
    setIsLoading(true)
    try {
      const response = await window.api.window.minimize()
      return response
    } finally {
      setIsLoading(false)
    }
  }, [])

  const maximize = useCallback(async (): Promise<ApiResponse<{ isMaximized: boolean }>> => {
    setIsLoading(true)
    try {
      const response = await window.api.window.maximize()
      return response
    } finally {
      setIsLoading(false)
    }
  }, [])

  const close = useCallback(async (): Promise<ApiResponse> => {
    setIsLoading(true)
    try {
      const response = await window.api.window.close()
      return response
    } finally {
      setIsLoading(false)
    }
  }, [])

  const getState = useCallback(async (): Promise<ApiResponse<WindowState>> => {
    return await window.api.window.getState()
  }, [])

  const setTitle = useCallback(async (title: string): Promise<ApiResponse> => {
    return await window.api.window.setTitle(title)
  }, [])

  return {
    minimize,
    maximize,
    close,
    getState,
    setTitle,
    isLoading
  }
}
