import { ElectronAPI } from '@electron-toolkit/preload'
import type { WindowState, ApiResponse, Tab, AppSettings } from '../shared/types'

declare global {
  interface Window {
    electron: ElectronAPI
    api: {
      window: {
        minimize: () => Promise<ApiResponse>
        maximize: () => Promise<ApiResponse<{ isMaximized: boolean }>>
        close: () => Promise<ApiResponse>
        getState: () => Promise<ApiResponse<WindowState>>
        setTitle: (title: string) => Promise<ApiResponse>
        setBounds: (bounds: {
          x: number
          y: number
          width: number
          height: number
        }) => Promise<ApiResponse>
        center: () => Promise<ApiResponse>
        onStateChanged: (callback: (state: Partial<WindowState>) => void) => () => void
      }
      titlebar: {
        showContextMenu: (x: number, y: number) => Promise<ApiResponse>
      }
      database: {
        saveTabs: (tabs: Tab[]) => Promise<ApiResponse>
        getTabs: () => Promise<ApiResponse<Tab[]>>
        saveSetting: (key: string, value: any) => Promise<ApiResponse>
        getSetting: (key: string, defaultValue?: any) => Promise<ApiResponse<any>>
        getAllSettings: () => Promise<ApiResponse<AppSettings>>
        saveWindowState: (state: WindowState) => Promise<ApiResponse>
        getWindowState: () => Promise<ApiResponse<WindowState | null>>
        cleanup: () => Promise<ApiResponse>
      }
      system: {
        getPlatform: () => string
        getVersions: () => NodeJS.ProcessVersions
      }
    }
  }
}
