import type Database from 'better-sqlite3'
import type { DatabaseTab } from '../../../shared/types'

export class TabRepository {
  private db: Database.Database

  constructor(database: Database.Database) {
    this.db = database
  }

  /**
   * 保存标签页数据
   */
  async saveTabs(tabs: DatabaseTab[]): Promise<void> {
    const transaction = this.db.transaction(() => {
      // 清空现有标签页
      this.db.prepare('DELETE FROM tabs').run()

      // 插入新标签页
      const insertTab = this.db.prepare(`
        INSERT INTO tabs (
          id, title, active, isDirty, isPinned, icon, closable, 
          tooltip, lastModified, metadata, path, type, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      for (const tab of tabs) {
        insertTab.run(
          tab.id,
          tab.title,
          tab.active,
          tab.isDirty,
          tab.isPinned,
          tab.icon,
          tab.closable,
          tab.tooltip,
          tab.lastModified,
          tab.metadata,
          tab.path,
          tab.type,
          tab.createdAt,
          tab.updatedAt
        )
      }
    })

    transaction()
  }

  /**
   * 获取所有标签页
   */
  async getTabs(): Promise<DatabaseTab[]> {
    const stmt = this.db.prepare('SELECT * FROM tabs ORDER BY createdAt ASC')
    return stmt.all() as DatabaseTab[]
  }

  /**
   * 根据ID获取标签页
   */
  async getTabById(id: string): Promise<DatabaseTab | undefined> {
    const stmt = this.db.prepare('SELECT * FROM tabs WHERE id = ?')
    return stmt.get(id) as DatabaseTab | undefined
  }

  /**
   * 更新标签页
   */
  async updateTab(id: string, updates: Partial<DatabaseTab>): Promise<void> {
    const fields = Object.keys(updates).filter(key => key !== 'id')
    if (fields.length === 0) return

    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => updates[field as keyof DatabaseTab])
    
    const stmt = this.db.prepare(`
      UPDATE tabs SET ${setClause}, updatedAt = ? WHERE id = ?
    `)
    
    stmt.run(...values, new Date().toISOString(), id)
  }

  /**
   * 删除标签页
   */
  async deleteTab(id: string): Promise<void> {
    const stmt = this.db.prepare('DELETE FROM tabs WHERE id = ?')
    stmt.run(id)
  }

  /**
   * 清理旧的标签页记录
   */
  async cleanupOldTabs(daysToKeep: number = 30): Promise<void> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

    const stmt = this.db.prepare('DELETE FROM tabs WHERE updatedAt < ?')
    stmt.run(cutoffDate.toISOString())
  }
}
